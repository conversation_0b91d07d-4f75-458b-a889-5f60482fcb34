// Simple test to verify the symptom checker service works correctly
const { getSymptoms, getDiagnosis } = require('./src/services/mockSymptomCheckerService.ts');

async function testSymptomChecker() {
  try {
    console.log('Testing symptom checker service...\n');
    
    // Test 1: Get symptoms
    console.log('1. Testing getSymptoms()...');
    const symptoms = await getSymptoms();
    console.log(`✓ Retrieved ${symptoms.length} symptoms`);
    
    // Check for duplicates
    const symptomIds = symptoms.map(s => s.id);
    const uniqueIds = new Set(symptomIds);
    if (symptomIds.length === uniqueIds.size) {
      console.log('✓ No duplicate symptom IDs found');
    } else {
      console.log('✗ Duplicate symptom IDs found!');
      const duplicates = symptomIds.filter((id, index) => symptomIds.indexOf(id) !== index);
      console.log('Duplicates:', [...new Set(duplicates)]);
    }
    
    // Test 2: Test diagnosis with common cold symptoms
    console.log('\n2. Testing getDiagnosis() with common cold symptoms...');
    const coldSymptoms = ['s_3', 's_4', 's_15', 's_16', 's_24']; // cough, sore throat, runny nose, congestion, sneezing
    const diagnosis = await getDiagnosis(coldSymptoms, 'female', 30);
    console.log(`✓ Got diagnosis for ${diagnosis.conditions.length} conditions`);
    console.log('Top condition:', diagnosis.conditions[0]?.name, 'with probability:', diagnosis.conditions[0]?.probability);
    
    // Test 3: Test diagnosis with diabetes symptoms
    console.log('\n3. Testing getDiagnosis() with diabetes symptoms...');
    const diabetesSymptoms = ['s_121', 's_122', 's_123', 's_473']; // excessive thirst, hunger, weight loss, frequent urination
    const diabetesDiagnosis = await getDiagnosis(diabetesSymptoms, 'male', 55);
    console.log(`✓ Got diagnosis for ${diabetesDiagnosis.conditions.length} conditions`);
    console.log('Top condition:', diabetesDiagnosis.conditions[0]?.name, 'with probability:', diabetesDiagnosis.conditions[0]?.probability);
    
    // Test 4: Test emergency symptoms
    console.log('\n4. Testing emergency symptoms...');
    const emergencySymptoms = ['s_9', 's_8']; // chest pain, shortness of breath
    const emergencyDiagnosis = await getDiagnosis(emergencySymptoms, 'male', 65);
    console.log('Triage level:', emergencyDiagnosis.triage.triage_level);
    if (emergencyDiagnosis.triage.triage_level === 'emergency') {
      console.log('✓ Emergency symptoms correctly detected');
    } else {
      console.log('✗ Emergency symptoms not detected');
    }
    
    console.log('\n✓ All tests completed successfully!');
    
  } catch (error) {
    console.error('✗ Test failed:', error.message);
  }
}

// Run the test
testSymptomChecker();
