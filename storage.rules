rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images
    match /users/{userId}/profile/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Medical records
    match /users/{userId}/medical_records/{allPaths=**} {
      allow read: if request.auth != null && (
        request.auth.uid == userId || 
        exists(/databases/$(database)/documents/doctorAssignments/$(userId)/doctors/$(request.auth.uid))
      );
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Test results
    match /users/{userId}/test_results/{allPaths=**} {
      allow read: if request.auth != null && (
        request.auth.uid == userId || 
        exists(/databases/$(database)/documents/doctorAssignments/$(userId)/doctors/$(request.auth.uid))
      );
      allow write: if request.auth != null && (
        request.auth.uid == userId || 
        exists(/databases/$(database)/documents/doctorAssignments/$(userId)/doctors/$(request.auth.uid))
      );
    }
    
    // Prescriptions
    match /users/{userId}/prescriptions/{allPaths=**} {
      allow read: if request.auth != null && (
        request.auth.uid == userId || 
        exists(/databases/$(database)/documents/doctorAssignments/$(userId)/doctors/$(request.auth.uid))
      );
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';
    }
    
    // Health resources
    match /health_resources/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && 
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor' || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Doctor notes
    match /users/{userId}/doctor_notes/{allPaths=**} {
      allow read: if request.auth != null && (
        request.auth.uid == userId || 
        exists(/databases/$(database)/documents/doctorAssignments/$(userId)/doctors/$(request.auth.uid))
      );
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';
    }
  }
}
