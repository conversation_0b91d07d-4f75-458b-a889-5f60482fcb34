# Test Plan for Chat and Request Fixes

## Issues Fixed

### 1. ✅ Chat Conversation Creation Error
- **Fixed**: Avatar undefined error in `chatService.ts`
- **Test**: Try to start a chat after doctor accepts request

### 2. ✅ Request Status Updates
- **Fixed**: Real-time request status updates in `ChatPage.tsx`
- **Test**: Send request, have doctor accept, check if UI updates

### 3. ✅ Online/Offline Status
- **Fixed**: User presence system in `chatService.ts`
- **Test**: Check if doctors show online/offline status

### 4. ✅ Request Button Changes
- **Fixed**: UI updates based on request status
- **Test**: Check if request button changes to "Connected" after acceptance

## Testing Steps

### Step 1: Test Request Flow
1. **Student Side**:
   - Login as student
   - Go to Chat page
   - Find a doctor and click "Request"
   - Verify button changes to "Requested"

2. **Doctor Side**:
   - <PERSON>gin as doctor
   - Go to Doctor Dashboard
   - Check "Students" tab for new request
   - Accept the request

3. **Student Side Again**:
   - Check if button changes to "Connected"
   - Try clicking on the doctor to start chat
   - Verify chat conversation is created

### Step 2: Test Chat Creation
1. After doctor accepts request
2. Student clicks on doctor with "Connected" status
3. Chat conversation should be created automatically
4. No "Failed to start conversation" error should appear

### Step 3: Test Online/Offline Status
1. Check if doctors show green dot when online
2. Check if doctors show gray dot when offline
3. Verify presence updates in real-time

### Step 4: Test Real-time Updates
1. Have doctor accept request in another browser/tab
2. Student side should update automatically within 10 seconds
3. Notification should appear when request is accepted

## Expected Behavior

### Before Request
- Doctor shows "Request" button
- Student can click to send request
- Doctor appears offline/online based on presence

### After Request Sent
- Button changes to "Requested" (orange chip)
- Student cannot click on doctor to chat
- Doctor receives notification

### After Request Accepted
- Button changes to "Connected" (green chip)
- Student can click on doctor to start chat
- Chat conversation is created automatically
- Student receives acceptance notification

### Chat Functionality
- Voice and video calls should work
- Messages should send without avatar errors
- Real-time messaging should work

## Debug Commands

Open browser console and run:
```javascript
// Check communication flow
window.debugCommunicationFlow()

// Check current user
console.log('Current user:', firebase.auth().currentUser)

// Check doctors list
console.log('Doctors:', window.allDoctors)
```

## Common Issues to Check

1. **Avatar Undefined Error**: Should be fixed with null fallbacks
2. **Request Status Not Updating**: Should update in real-time
3. **Chat Not Creating**: Should work after acceptance
4. **Online Status**: Should show green/gray dots
5. **Button Not Changing**: Should change from Request → Requested → Connected

## Success Criteria

✅ Students can send requests to doctors  
✅ Doctors can accept/reject requests  
✅ UI updates in real-time when requests are accepted  
✅ Chat conversations are created automatically after acceptance  
✅ Online/offline status is displayed correctly  
✅ No avatar undefined errors in chat creation  
✅ Voice and video calls work in chat  
✅ Notifications are sent when requests are accepted  
