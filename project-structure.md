src/
├── components/             # Reusable UI components
│   ├── layout/             # Layout components
│   ├── auth/               # Authentication components
│   └── common/             # Common UI elements
├── features/               # Feature-specific components
│   ├── symptomChecker/     # Symptom checker feature
│   ├── fileManagement/     # File management feature
│   ├── communication/      # Communication portal
│   └── admin/              # Admin dashboard
├── services/               # API and Firebase services
├── hooks/                  # Custom React hooks
├── utils/                  # Utility functions
└── types/                  # TypeScript type definitions