// Request Management Service
// Handles student requests to connect with doctors

import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';
import { getAllUsers } from './authService';

export interface ConnectionRequest {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  doctorId: string;
  doctorName: string;
  doctorEmail: string;
  message?: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  respondedAt?: Timestamp;
}

/**
 * Create a connection request from student to doctor
 */
export const createConnectionRequest = async (
  studentId: string,
  doctorId: string,
  message?: string
): Promise<boolean> => {
  try {
    // Get student and doctor details
    const allUsers = await getAllUsers();
    const student = allUsers.find(u => u.uid === studentId);
    const doctor = allUsers.find(u => u.uid === doctorId);

    if (!student || !doctor) {
      throw new Error('Student or doctor not found');
    }

    // Check if request already exists
    const existingRequestQuery = query(
      collection(db, 'connectionRequests'),
      where('studentId', '==', studentId),
      where('doctorId', '==', doctorId),
      where('status', '==', 'pending')
    );

    const existingRequests = await getDocs(existingRequestQuery);
    if (!existingRequests.empty) {
      console.log('Request already exists');
      return false;
    }

    // Create new request
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const requestData: Omit<ConnectionRequest, 'id'> = {
      studentId,
      studentName: student.displayName || student.email || 'Unknown Student',
      studentEmail: student.email,
      doctorId,
      doctorName: doctor.displayName || doctor.email || 'Unknown Doctor',
      doctorEmail: doctor.email,
      message: message || '',
      status: 'pending',
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp
    };

    await setDoc(doc(db, 'connectionRequests', requestId), requestData);

    console.log(`✅ Connection request created: ${requestId}`);
    console.log('Request data:', requestData);

    // Send notification to doctor
    try {
      const { notifyDoctorOfConnectionRequest } = await import('./notificationService');
      await notifyDoctorOfConnectionRequest(doctorId, requestData.studentName, requestId);
      console.log(`📧 Notification sent to doctor ${doctorId}`);
    } catch (notificationError) {
      console.error('Failed to send notification:', notificationError);
      // Don't fail the request creation if notification fails
    }

    return true;
  } catch (error) {
    console.error('Error creating connection request:', error);
    return false;
  }
};

/**
 * Get all pending requests for a doctor
 */
export const getDoctorRequests = async (doctorId: string): Promise<ConnectionRequest[]> => {
  try {
    console.log(`🔍 Looking for requests for doctor: ${doctorId}`);

    // First, let's check all requests for this doctor regardless of status
    const allRequestsQuery = query(
      collection(db, 'connectionRequests'),
      where('doctorId', '==', doctorId)
    );

    const allSnapshot = await getDocs(allRequestsQuery);
    console.log(`📊 Total requests for doctor ${doctorId}: ${allSnapshot.docs.length}`);

    if (allSnapshot.docs.length > 0) {
      console.log('All requests:', allSnapshot.docs.map(doc => ({
        id: doc.id,
        status: doc.data().status,
        studentName: doc.data().studentName,
        createdAt: doc.data().createdAt
      })));
    }

    // Now get only pending requests (removed orderBy to avoid index requirement)
    const requestsQuery = query(
      collection(db, 'connectionRequests'),
      where('doctorId', '==', doctorId),
      where('status', '==', 'pending')
    );

    const snapshot = await getDocs(requestsQuery);
    const requests = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ConnectionRequest[];

    // Sort by createdAt on client side (most recent first)
    requests.sort((a, b) => {
      const dateA = a.createdAt?.toDate?.() || new Date(0);
      const dateB = b.createdAt?.toDate?.() || new Date(0);
      return dateB.getTime() - dateA.getTime();
    });

    console.log(`📋 Found ${requests.length} pending requests for doctor ${doctorId}`);

    if (requests.length > 0) {
      console.log('Pending requests:', requests.map(r => ({
        id: r.id,
        studentName: r.studentName,
        status: r.status,
        message: r.message
      })));
    }

    return requests;
  } catch (error) {
    console.error('Error getting doctor requests:', error);
    console.error('Error details:', error);
    return [];
  }
};

/**
 * Get all requests made by a student
 */
export const getStudentRequests = async (studentId: string): Promise<ConnectionRequest[]> => {
  try {
    const requestsQuery = query(
      collection(db, 'connectionRequests'),
      where('studentId', '==', studentId),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(requestsQuery);
    const requests = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ConnectionRequest[];

    return requests;
  } catch (error) {
    console.error('Error getting student requests:', error);
    return [];
  }
};

/**
 * Accept a connection request
 */
export const acceptConnectionRequest = async (requestId: string): Promise<boolean> => {
  try {
    // Get request details first
    const requestDoc = await getDoc(doc(db, 'connectionRequests', requestId));
    if (!requestDoc.exists()) {
      throw new Error('Request not found');
    }

    const requestData = requestDoc.data() as ConnectionRequest;

    await updateDoc(doc(db, 'connectionRequests', requestId), {
      status: 'accepted',
      respondedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Create a conversation between student and doctor
    try {
      const { createConversationBetweenUsers } = await import('../hooks/useChat');
      const conversationId = await createConversationBetweenUsers(
        requestData.studentId,
        requestData.doctorId
      );
      console.log(`💬 Conversation created: ${conversationId}`);
    } catch (conversationError) {
      console.error('Failed to create conversation:', conversationError);
      // Don't fail the acceptance if conversation creation fails
    }

    // Send notification to student
    try {
      const { notifyStudentOfAcceptedRequest } = await import('./notificationService');
      await notifyStudentOfAcceptedRequest(
        requestData.studentId,
        requestData.doctorName,
        requestId
      );
      console.log(`📧 Notification sent to student ${requestData.studentId}`);
    } catch (notificationError) {
      console.error('Failed to send acceptance notification:', notificationError);
      // Don't fail the acceptance if notification fails
    }

    console.log(`✅ Request ${requestId} accepted, conversation created, and student notified`);
    return true;
  } catch (error) {
    console.error('Error accepting request:', error);
    return false;
  }
};

/**
 * Reject a connection request
 */
export const rejectConnectionRequest = async (requestId: string): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'connectionRequests', requestId), {
      status: 'rejected',
      respondedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log(`❌ Request ${requestId} rejected`);
    return true;
  } catch (error) {
    console.error('Error rejecting request:', error);
    return false;
  }
};

/**
 * Get request statistics for a doctor
 */
export const getDoctorRequestStats = async (doctorId: string) => {
  try {
    const allRequestsQuery = query(
      collection(db, 'connectionRequests'),
      where('doctorId', '==', doctorId)
    );

    const snapshot = await getDocs(allRequestsQuery);
    const requests = snapshot.docs.map(doc => doc.data());

    const pending = requests.filter(r => r.status === 'pending').length;
    const accepted = requests.filter(r => r.status === 'accepted').length;
    const rejected = requests.filter(r => r.status === 'rejected').length;

    return {
      total: requests.length,
      pending,
      accepted,
      rejected
    };
  } catch (error) {
    console.error('Error getting request stats:', error);
    return { total: 0, pending: 0, accepted: 0, rejected: 0 };
  }
};

/**
 * Debug function to test request creation
 */
export const debugRequestSystem = async (doctorId: string) => {
  try {
    console.log('🔬 DEBUGGING REQUEST SYSTEM');
    console.log('============================');

    // Check if we can access the collection
    const testQuery = query(collection(db, 'connectionRequests'));
    const testSnapshot = await getDocs(testQuery);
    console.log(`📊 Total requests in database: ${testSnapshot.docs.length}`);

    // Check requests for this specific doctor
    const doctorRequests = await getDoctorRequests(doctorId);
    console.log(`👨‍⚕️ Requests for doctor ${doctorId}: ${doctorRequests.length}`);

    // Get all users to see available students
    const { getAllUsers } = await import('./authService');
    const allUsers = await getAllUsers();
    const students = allUsers.filter(u => u.role === 'student');
    console.log(`👨‍🎓 Available students: ${students.length}`);

    if (students.length > 0) {
      console.log('Sample students:', students.slice(0, 3).map(s => ({
        uid: s.uid,
        email: s.email,
        displayName: s.displayName
      })));
    }

    return {
      totalRequests: testSnapshot.docs.length,
      doctorRequests: doctorRequests.length,
      availableStudents: students.length,
      doctorId
    };
  } catch (error) {
    console.error('❌ Debug failed:', error);
    return { error: error.message };
  }
};

/**
 * Create a test request for debugging
 */
export const createTestRequest = async (doctorId: string) => {
  try {
    // Get a student to create test request from
    const { getAllUsers } = await import('./authService');
    const allUsers = await getAllUsers();
    const students = allUsers.filter(u => u.role === 'student');

    if (students.length === 0) {
      throw new Error('No students available to create test request');
    }

    const testStudent = students[0];
    console.log(`Creating test request from student: ${testStudent.displayName || testStudent.email}`);

    const success = await createConnectionRequest(
      testStudent.uid,
      doctorId,
      'This is a test connection request for debugging purposes.'
    );

    if (success) {
      console.log('✅ Test request created successfully');
      return { success: true, studentName: testStudent.displayName || testStudent.email };
    } else {
      console.log('❌ Test request creation failed');
      return { success: false, error: 'Creation failed' };
    }
  } catch (error) {
    console.error('❌ Error creating test request:', error);
    return { success: false, error: error.message };
  }
};

// Make debug functions available globally
if (typeof window !== 'undefined') {
  (window as any).debugRequestSystem = debugRequestSystem;
  (window as any).createTestRequest = createTestRequest;
  (window as any).getDoctorRequests = getDoctorRequests;
}
