import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';
import { uploadFile, getFileUrl, STORAGE_BUCKETS } from './supabase';

export interface HealthResource {
  id: string;
  title: string;
  content: string;
  summary: string;
  category: string;
  tags: string[];
  authorId: string;
  authorName: string;
  coverImageUrl?: string;
  coverImagePath?: string;
  status: 'draft' | 'published' | 'archived';
  publishedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  views: number;
  likes: number;
}

export interface CreateHealthResourceData {
  title: string;
  content: string;
  summary: string;
  category: string;
  tags: string[];
  authorId: string;
  authorName: string;
  coverImage?: File;
  status: 'draft' | 'published';
}

export interface UpdateHealthResourceData {
  title?: string;
  content?: string;
  summary?: string;
  category?: string;
  tags?: string[];
  coverImage?: File;
  status?: 'draft' | 'published' | 'archived';
}

/**
 * Create a new health resource
 */
export const createHealthResource = async (data: CreateHealthResourceData): Promise<string> => {
  try {
    let coverImageUrl = '';
    let coverImagePath = '';

    // Upload cover image if provided
    if (data.coverImage) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${timestamp}_${data.coverImage.name}`;
      coverImagePath = `${data.authorId}/health-resources/${fileName}`;

      await uploadFile(STORAGE_BUCKETS.HEALTH_TIPS_MEDIA, coverImagePath, data.coverImage);
      coverImageUrl = getFileUrl(STORAGE_BUCKETS.HEALTH_TIPS_MEDIA, coverImagePath);
    }

    const resourceData: Omit<HealthResource, 'id'> = {
      title: data.title,
      content: data.content,
      summary: data.summary,
      category: data.category,
      tags: data.tags,
      authorId: data.authorId,
      authorName: data.authorName,
      coverImageUrl,
      coverImagePath,
      status: data.status,
      publishedAt: data.status === 'published' ? Timestamp.now() : undefined,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      views: 0,
      likes: 0
    };

    const docRef = await addDoc(collection(db, 'healthResources'), resourceData);
    console.log('Health resource created successfully:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating health resource:', error);
    throw error;
  }
};

/**
 * Update an existing health resource
 */
export const updateHealthResource = async (
  resourceId: string,
  data: UpdateHealthResourceData
): Promise<void> => {
  try {
    const updateData: any = {
      ...data,
      updatedAt: Timestamp.now()
    };

    // Handle cover image update
    if (data.coverImage) {
      const resourceDoc = await getDoc(doc(db, 'healthResources', resourceId));
      if (resourceDoc.exists()) {
        const resourceData = resourceDoc.data() as HealthResource;

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `${timestamp}_${data.coverImage.name}`;
        const coverImagePath = `${resourceData.authorId}/health-resources/${fileName}`;

        await uploadFile(STORAGE_BUCKETS.HEALTH_TIPS_MEDIA, coverImagePath, data.coverImage);
        const coverImageUrl = getFileUrl(STORAGE_BUCKETS.HEALTH_TIPS_MEDIA, coverImagePath);

        updateData.coverImageUrl = coverImageUrl;
        updateData.coverImagePath = coverImagePath;
      }
    }

    // Set published date if status changes to published
    if (data.status === 'published') {
      updateData.publishedAt = Timestamp.now();
    }

    await updateDoc(doc(db, 'healthResources', resourceId), updateData);
    console.log('Health resource updated successfully:', resourceId);
  } catch (error) {
    console.error('Error updating health resource:', error);
    throw error;
  }
};

/**
 * Delete a health resource
 */
export const deleteHealthResource = async (resourceId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'healthResources', resourceId));
    console.log('Health resource deleted successfully:', resourceId);
  } catch (error) {
    console.error('Error deleting health resource:', error);
    throw error;
  }
};

/**
 * Get all health resources by author
 */
export const getHealthResourcesByAuthor = async (authorId: string): Promise<HealthResource[]> => {
  try {
    const q = query(
      collection(db, 'healthResources'),
      where('authorId', '==', authorId),
      orderBy('updatedAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as HealthResource[];
  } catch (error) {
    console.error('Error getting health resources by author:', error);
    return [];
  }
};

/**
 * Get published health resources for students
 */
export const getPublishedHealthResources = async (limitCount: number = 20): Promise<HealthResource[]> => {
  try {
    console.log('🔍 Querying published health resources...');

    // First try a simple query without orderBy to avoid index issues
    const q = query(
      collection(db, 'healthResources'),
      where('status', '==', 'published'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    console.log(`📊 Found ${snapshot.size} published health resources`);

    const resources = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as HealthResource[];

    // Debug: Log image URLs
    resources.forEach((resource, index) => {
      console.log(`Resource ${index + 1} image info:`, {
        title: resource.title,
        coverImageUrl: resource.coverImageUrl,
        coverImagePath: resource.coverImagePath,
        hasImage: !!resource.coverImageUrl
      });
    });

    // Sort manually by publishedAt if needed
    resources.sort((a, b) => {
      if (!a.publishedAt || !b.publishedAt) return 0;
      return b.publishedAt.toDate().getTime() - a.publishedAt.toDate().getTime();
    });

    console.log('✅ Successfully retrieved and sorted health resources');
    return resources;
  } catch (error) {
    console.error('❌ Error getting published health resources:', error);
    console.error('Error details:', error);

    // Fallback: try without any filters
    try {
      console.log('🔄 Trying fallback query without filters...');
      const fallbackQuery = query(collection(db, 'healthResources'));
      const fallbackSnapshot = await getDocs(fallbackQuery);

      const allResources = fallbackSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as HealthResource[];

      // Filter published resources manually
      const publishedResources = allResources.filter(resource => resource.status === 'published');
      console.log(`📊 Fallback found ${publishedResources.length} published resources out of ${allResources.length} total`);

      return publishedResources.slice(0, limitCount);
    } catch (fallbackError) {
      console.error('❌ Fallback query also failed:', fallbackError);
      return [];
    }
  }
};

/**
 * Get health resources by category
 */
export const getHealthResourcesByCategory = async (category: string): Promise<HealthResource[]> => {
  try {
    const q = query(
      collection(db, 'healthResources'),
      where('category', '==', category),
      where('status', '==', 'published'),
      orderBy('publishedAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as HealthResource[];
  } catch (error) {
    console.error('Error getting health resources by category:', error);
    return [];
  }
};

/**
 * Get a single health resource by ID
 */
export const getHealthResourceById = async (resourceId: string): Promise<HealthResource | null> => {
  try {
    const docSnap = await getDoc(doc(db, 'healthResources', resourceId));

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as HealthResource;
    }

    return null;
  } catch (error) {
    console.error('Error getting health resource:', error);
    return null;
  }
};

/**
 * Increment view count for a health resource
 */
export const incrementResourceViews = async (resourceId: string): Promise<void> => {
  try {
    const resourceRef = doc(db, 'healthResources', resourceId);
    await updateDoc(resourceRef, {
      views: Timestamp.now() // You might want to use increment() instead
    });
  } catch (error) {
    console.error('Error incrementing resource views:', error);
  }
};

/**
 * Get health resource statistics for doctor dashboard
 */
export const getHealthResourceStats = async (authorId: string) => {
  try {
    const resources = await getHealthResourcesByAuthor(authorId);

    const published = resources.filter(r => r.status === 'published').length;
    const draft = resources.filter(r => r.status === 'draft').length;
    const totalViews = resources.reduce((sum, r) => sum + (r.views || 0), 0);

    return {
      total: resources.length,
      published,
      draft,
      totalViews
    };
  } catch (error) {
    console.error('Error getting health resource stats:', error);
    return {
      total: 0,
      published: 0,
      draft: 0,
      totalViews: 0
    };
  }
};

/**
 * Test function to verify health resources are being fetched correctly
 * Call this from browser console: window.testHealthResources()
 */
export const testHealthResources = async () => {
  console.log('Testing health resources fetch...');
  try {
    const resources = await getPublishedHealthResources(10);
    console.log(`✅ Found ${resources.length} published health resources:`);
    resources.forEach((resource, index) => {
      console.log(`${index + 1}. ${resource.title} - ${resource.category}`);
      console.log(`   Author: ${resource.authorName}`);
      console.log(`   Published: ${resource.publishedAt ? new Date(resource.publishedAt.toDate()).toLocaleDateString() : 'No date'}`);
      console.log(`   Cover Image: ${resource.coverImageUrl ? 'Yes' : 'No'}`);
      console.log('---');
    });
    return { success: true, count: resources.length, resources };
  } catch (error) {
    console.error('❌ Error fetching health resources:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Debug function to check Firebase connection and health resources collection
 */
export const debugHealthResourcesCollection = async () => {
  console.log('🔍 Debugging health resources collection...');
  try {
    // Check if we can access the collection at all
    const allResourcesQuery = query(collection(db, 'healthResources'));
    const allSnapshot = await getDocs(allResourcesQuery);

    console.log(`📊 Total documents in healthResources collection: ${allSnapshot.size}`);

    if (allSnapshot.size === 0) {
      console.log('❌ No documents found in healthResources collection!');
      console.log('💡 Make sure you have created some health resources as a doctor');
      return { success: false, error: 'No documents in collection' };
    }

    // Log all documents
    allSnapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`Document ${index + 1}:`, {
        id: doc.id,
        title: data.title,
        status: data.status,
        publishedAt: data.publishedAt,
        authorName: data.authorName
      });
    });

    // Check published resources specifically
    const publishedQuery = query(
      collection(db, 'healthResources'),
      where('status', '==', 'published')
    );
    const publishedSnapshot = await getDocs(publishedQuery);
    console.log(`📈 Published resources: ${publishedSnapshot.size}`);

    return {
      success: true,
      total: allSnapshot.size,
      published: publishedSnapshot.size,
      documents: allSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    };
  } catch (error) {
    console.error('❌ Error debugging collection:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Make test functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).testHealthResources = testHealthResources;
  (window as any).debugHealthResourcesCollection = debugHealthResourcesCollection;
}
