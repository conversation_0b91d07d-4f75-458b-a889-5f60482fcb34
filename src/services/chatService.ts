import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  getDocs,
  getDoc,
  serverTimestamp,
  increment,
  arrayUnion,
  writeBatch,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';
import type {
  Conversation,
  Message,
  TypingIndicator,
  UserPresence,
  ChatService
} from '../types/chat';

class FirebaseChatService implements ChatService {
  private conversationsRef = collection(db, 'conversations');
  private messagesRef = collection(db, 'messages');
  private typingRef = collection(db, 'typing');
  private presenceRef = collection(db, 'presence');

  // Conversations
  async createConversation(userId: string, doctorId: string): Promise<string> {
    try {
      console.log('Creating conversation between:', { userId, doctorId });

      // Validate inputs
      if (!userId || !doctorId) {
        throw new Error('Both userId and doctorId are required');
      }

      if (userId === doctorId) {
        throw new Error('Cannot create conversation with yourself');
      }

      // Check if conversation already exists
      const existingQuery = query(
        this.conversationsRef,
        where('participants', 'array-contains', userId)
      );

      const existingDocs = await getDocs(existingQuery);
      const existingConversation = existingDocs.docs.find(doc => {
        const data = doc.data();
        return data.participants.includes(doctorId);
      });

      if (existingConversation) {
        console.log('Found existing conversation:', existingConversation.id);
        return existingConversation.id;
      }

      // Get user and doctor details
      console.log('Fetching user and doctor details...');
      const userDoc = await getDoc(doc(db, 'users', userId));
      const doctorDoc = await getDoc(doc(db, 'users', doctorId));

      if (!userDoc.exists()) {
        throw new Error(`User not found: ${userId}`);
      }

      if (!doctorDoc.exists()) {
        throw new Error(`Doctor not found: ${doctorId}`);
      }

      const userData = userDoc.data();
      const doctorData = doctorDoc.data();

      // Create new conversation with proper fallback values
      const conversationData: Omit<Conversation, 'id'> = {
        participants: [userId, doctorId],
        participantDetails: {
          [userId]: {
            name: userData.displayName || userData.email || 'User',
            role: 'user',
            avatar: userData.photoURL || userData.avatar || null // Ensure avatar is never undefined
          },
          [doctorId]: {
            name: doctorData.displayName || doctorData.email || 'Doctor',
            role: 'doctor',
            avatar: doctorData.photoURL || doctorData.avatar || null, // Ensure avatar is never undefined
            specialty: doctorData.specialty || 'General Practice'
          }
        },
        lastActivity: serverTimestamp() as Timestamp,
        unreadCounts: {
          [userId]: 0,
          [doctorId]: 0
        },
        status: 'active',
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp
      };

      console.log('Creating new conversation with data:', conversationData);
      const docRef = await addDoc(this.conversationsRef, conversationData);
      console.log('Conversation created successfully:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('Error creating conversation:', error);
      console.error('Error details:', {
        userId,
        doctorId,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async getConversations(userId: string): Promise<Conversation[]> {
    try {
      const q = query(
        this.conversationsRef,
        where('participants', 'array-contains', userId),
        orderBy('lastActivity', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Conversation));
    } catch (error) {
      console.error('Error getting conversations:', error);
      throw error;
    }
  }

  subscribeToConversations(userId: string, callback: (conversations: Conversation[]) => void): () => void {
    const q = query(
      this.conversationsRef,
      where('participants', 'array-contains', userId),
      orderBy('lastActivity', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const conversations = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Conversation));
      callback(conversations);
    }, (error) => {
      console.error('Error in conversations subscription:', error);
    });
  }

  // Messages
  async sendMessage(conversationId: string, senderId: string, content: string, type: string = 'text'): Promise<string> {
    try {
      const batch = writeBatch(db);

      // Get sender details
      const senderDoc = await getDoc(doc(db, 'users', senderId));
      if (!senderDoc.exists()) {
        throw new Error('Sender not found');
      }

      const senderData = senderDoc.data();
      const senderRole = senderData.role === 'doctor' ? 'doctor' : 'user';

      // Create message with proper fallback values
      const messageData: Omit<Message, 'id'> = {
        conversationId,
        senderId,
        senderRole,
        senderName: senderData.displayName || senderData.email || 'User',
        senderAvatar: senderData.photoURL || senderData.avatar || null, // Ensure avatar is never undefined
        content,
        type: type as any,
        timestamp: serverTimestamp() as Timestamp,
        readBy: [senderId], // Sender has read their own message
        edited: false
      };

      const messageRef = doc(this.messagesRef);
      batch.set(messageRef, messageData);

      // Update conversation
      const conversationRef = doc(this.conversationsRef, conversationId);
      const conversationDoc = await getDoc(conversationRef);

      if (conversationDoc.exists()) {
        const conversationData = conversationDoc.data() as Conversation;
        const otherParticipants = conversationData.participants.filter(id => id !== senderId);

        const unreadCounts: { [key: string]: any } = {};
        otherParticipants.forEach(participantId => {
          unreadCounts[`unreadCounts.${participantId}`] = increment(1);
        });

        batch.update(conversationRef, {
          lastMessage: {
            content,
            senderId,
            timestamp: serverTimestamp(),
            type
          },
          lastActivity: serverTimestamp(),
          updatedAt: serverTimestamp(),
          ...unreadCounts
        });
      }

      await batch.commit();
      return messageRef.id;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async getMessages(conversationId: string, messageLimit: number = 50): Promise<Message[]> {
    try {
      const q = query(
        this.messagesRef,
        where('conversationId', '==', conversationId),
        orderBy('timestamp', 'desc'),
        limit(messageLimit)
      );

      const snapshot = await getDocs(q);
      const messages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Message));

      return messages.reverse(); // Return in chronological order
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  subscribeToMessages(conversationId: string, callback: (messages: Message[]) => void): () => void {
    const q = query(
      this.messagesRef,
      where('conversationId', '==', conversationId),
      orderBy('timestamp', 'asc')
    );

    return onSnapshot(q, (snapshot) => {
      const messages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Message));
      callback(messages);
    }, (error) => {
      console.error('Error in messages subscription:', error);
    });
  }

  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      const batch = writeBatch(db);

      // Get unread messages
      const q = query(
        this.messagesRef,
        where('conversationId', '==', conversationId),
        where('readBy', 'not-in', [[userId]])
      );

      const snapshot = await getDocs(q);

      // Mark messages as read
      snapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          readBy: arrayUnion(userId)
        });
      });

      // Reset unread count in conversation
      const conversationRef = doc(this.conversationsRef, conversationId);
      batch.update(conversationRef, {
        [`unreadCounts.${userId}`]: 0
      });

      await batch.commit();
    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw error;
    }
  }

  // Typing indicators
  async setTyping(conversationId: string, userId: string, userName: string): Promise<void> {
    try {
      const typingDoc = doc(this.typingRef, `${conversationId}_${userId}`);
      await updateDoc(typingDoc, {
        conversationId,
        userId,
        userName,
        timestamp: serverTimestamp()
      }).catch(async () => {
        // Document doesn't exist, create it
        await addDoc(this.typingRef, {
          conversationId,
          userId,
          userName,
          timestamp: serverTimestamp()
        });
      });

      // Auto-remove typing indicator after 3 seconds
      setTimeout(async () => {
        try {
          await deleteDoc(typingDoc);
        } catch (error) {
          // Ignore errors when deleting typing indicator
        }
      }, 3000);
    } catch (error) {
      console.error('Error setting typing indicator:', error);
    }
  }

  subscribeToTyping(conversationId: string, callback: (typing: TypingIndicator[]) => void): () => void {
    const q = query(
      this.typingRef,
      where('conversationId', '==', conversationId)
    );

    return onSnapshot(q, (snapshot) => {
      const typing = snapshot.docs.map(doc => doc.data() as TypingIndicator);
      callback(typing);
    });
  }

  // Presence
  async setUserPresence(userId: string, status: 'online' | 'offline' | 'away' | 'busy'): Promise<void> {
    try {
      const presenceDoc = doc(this.presenceRef, userId);
      await setDoc(presenceDoc, {
        userId,
        status,
        lastSeen: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      console.error('Error setting user presence:', error);
    }
  }

  subscribeToPresence(userIds: string[], callback: (presence: UserPresence[]) => void): () => void {
    const q = query(
      this.presenceRef,
      where('userId', 'in', userIds)
    );

    return onSnapshot(q, (snapshot) => {
      const presence = snapshot.docs.map(doc => doc.data() as UserPresence);
      callback(presence);
    });
  }
}

// Export singleton instance
export const chatService = new FirebaseChatService();
export default chatService;
