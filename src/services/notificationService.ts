import {
  collection,
  doc,
  setDoc,
  getDocs,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';

export interface ExtendedNotification {
  id: string;
  userId: string;
  type: 'connection_request' | 'appointment_booked' | 'appointment_reminder' | 'message' | 'system' | 'appointment' | 'medication' | 'health_tip' | 'record';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export const createNotification = async (
  userId: string,
  type: ExtendedNotification['type'],
  title: string,
  message: string,
  data?: any
): Promise<boolean> => {
  try {
    const notificationId = `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const notificationData: Omit<ExtendedNotification, 'id'> = {
      userId,
      type,
      title,
      message,
      data: data || {},
      read: false,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp
    };

    await setDoc(doc(db, 'notifications', notificationId), notificationData);
    console.log(`📢 Notification created for user ${userId}: ${title}`);
    return true;
  } catch (error) {
    console.error('Error creating notification:', error);
    return false;
  }
};

export const getUnreadNotificationCount = async (userId: string): Promise<number> => {
  try {
    const notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      where('read', '==', false)
    );

    const snapshot = await getDocs(notificationsQuery);
    return snapshot.docs.length;
  } catch (error) {
    console.error('Error getting unread count:', error);
    return 0;
  }
};

export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'notifications', notificationId), {
      read: true,
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
};

// Specific notification creators
export const notifyDoctorOfConnectionRequest = async (
  doctorId: string,
  studentName: string,
  requestId: string
): Promise<boolean> => {
  return await createNotification(
    doctorId,
    'connection_request',
    'New Connection Request',
    `${studentName} wants to connect with you for healthcare consultation.`,
    { requestId, studentName }
  );
};

export const notifyStudentOfAcceptedRequest = async (
  studentId: string,
  doctorName: string,
  requestId: string
): Promise<boolean> => {
  return await createNotification(
    studentId,
    'connection_request',
    'Connection Request Accepted',
    `Dr. ${doctorName} has accepted your connection request. You can now chat with them.`,
    { requestId, doctorName, status: 'accepted' }
  );
};
