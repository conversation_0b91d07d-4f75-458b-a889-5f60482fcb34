import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';

export interface Appointment {
  id?: string;
  studentId: string;
  studentName: string;
  doctorId: string;
  doctorName: string;
  date: string;
  time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no-show';
  type: 'consultation' | 'follow-up' | 'emergency' | 'routine';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Create a new appointment
 */
export const createAppointment = async (appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, 'appointments'), {
      ...appointmentData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    console.log('✅ Appointment created with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('❌ Error creating appointment:', error);
    throw error;
  }
};

/**
 * Get appointment by ID
 */
export const getAppointmentById = async (appointmentId: string): Promise<Appointment | null> => {
  try {
    const docRef = doc(db, 'appointments', appointmentId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Appointment;
    }

    console.log(`📅 Appointment not found: ${appointmentId}`);
    return null;
  } catch (error) {
    console.error('❌ Error fetching appointment by ID:', error);
    return null;
  }
};

/**
 * Get all appointments
 */
export const getAllAppointments = async (): Promise<Appointment[]> => {
  try {
    const querySnapshot = await getDocs(
      query(collection(db, 'appointments'), orderBy('createdAt', 'desc'))
    );

    const appointments: Appointment[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      appointments.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Appointment);
    });

    console.log(`📅 Retrieved ${appointments.length} appointments`);
    return appointments;
  } catch (error) {
    console.error('❌ Error fetching appointments:', error);
    return [];
  }
};

/**
 * Get appointments for today
 */
export const getTodaysAppointments = async (): Promise<Appointment[]> => {
  try {
    const today = new Date().toISOString().split('T')[0];

    // First, get all appointments for today (without orderBy to avoid index requirement)
    const querySnapshot = await getDocs(
      query(
        collection(db, 'appointments'),
        where('date', '==', today)
      )
    );

    const appointments: Appointment[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      appointments.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Appointment);
    });

    // Sort by time on the client side
    appointments.sort((a, b) => {
      const timeA = a.time || '00:00';
      const timeB = b.time || '00:00';
      return timeA.localeCompare(timeB);
    });

    console.log(`📅 Retrieved ${appointments.length} appointments for today`);
    return appointments;
  } catch (error) {
    console.error('❌ Error fetching today\'s appointments:', error);
    return [];
  }
};

/**
 * Get recent appointments (last 10)
 */
export const getRecentAppointments = async (): Promise<Appointment[]> => {
  try {
    const querySnapshot = await getDocs(
      query(
        collection(db, 'appointments'),
        orderBy('createdAt', 'desc'),
        limit(10)
      )
    );

    const appointments: Appointment[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      appointments.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Appointment);
    });

    console.log(`📅 Retrieved ${appointments.length} recent appointments`);
    return appointments;
  } catch (error) {
    console.error('❌ Error fetching recent appointments:', error);
    return [];
  }
};

/**
 * Update appointment status
 */
export const updateAppointmentStatus = async (appointmentId: string, status: Appointment['status']): Promise<void> => {
  try {
    await updateDoc(doc(db, 'appointments', appointmentId), {
      status,
      updatedAt: Timestamp.now()
    });
    console.log('✅ Appointment status updated');
  } catch (error) {
    console.error('❌ Error updating appointment status:', error);
    throw error;
  }
};

/**
 * Delete appointment
 */
export const deleteAppointment = async (appointmentId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'appointments', appointmentId));
    console.log('✅ Appointment deleted');
  } catch (error) {
    console.error('❌ Error deleting appointment:', error);
    throw error;
  }
};

/**
 * Get appointment statistics
 */
export const getAppointmentStats = async () => {
  try {
    const [allAppointments, todaysAppointments] = await Promise.all([
      getAllAppointments(),
      getTodaysAppointments()
    ]);

    const thisMonth = new Date();
    thisMonth.setDate(1);
    const thisMonthAppointments = allAppointments.filter(apt =>
      new Date(apt.createdAt) >= thisMonth
    );

    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    lastMonth.setDate(1);
    const lastMonthEnd = new Date();
    lastMonthEnd.setDate(0);

    const lastMonthAppointments = allAppointments.filter(apt => {
      const aptDate = new Date(apt.createdAt);
      return aptDate >= lastMonth && aptDate <= lastMonthEnd;
    });

    const monthlyChange = lastMonthAppointments.length > 0
      ? Math.round(((thisMonthAppointments.length - lastMonthAppointments.length) / lastMonthAppointments.length) * 100)
      : 0;

    return {
      total: allAppointments.length,
      today: todaysAppointments.length,
      thisMonth: thisMonthAppointments.length,
      lastMonth: lastMonthAppointments.length,
      monthlyChange,
      trend: monthlyChange >= 0 ? 'up' : 'down'
    };
  } catch (error) {
    console.error('❌ Error getting appointment stats:', error);
    return {
      total: 0,
      today: 0,
      thisMonth: 0,
      lastMonth: 0,
      monthlyChange: 0,
      trend: 'up' as const
    };
  }
};

/**
 * Get appointments by doctor ID
 */
export const getAppointmentsByDoctor = async (doctorId: string): Promise<Appointment[]> => {
  try {
    // Get appointments without orderBy to avoid index requirement
    const querySnapshot = await getDocs(
      query(
        collection(db, 'appointments'),
        where('doctorId', '==', doctorId)
      )
    );

    const appointments: Appointment[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      appointments.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Appointment);
    });

    // Sort by date on the client side (most recent first)
    appointments.sort((a, b) => {
      const dateA = new Date(a.date || '1970-01-01');
      const dateB = new Date(b.date || '1970-01-01');
      return dateB.getTime() - dateA.getTime();
    });

    console.log(`📅 Retrieved ${appointments.length} appointments for doctor ${doctorId}`);
    return appointments;
  } catch (error) {
    console.error('❌ Error fetching doctor appointments:', error);
    return [];
  }
};

/**
 * Get appointments by student ID
 */
export const getAppointmentsByStudent = async (studentId: string): Promise<Appointment[]> => {
  try {
    // Get appointments without orderBy to avoid index requirement
    const querySnapshot = await getDocs(
      query(
        collection(db, 'appointments'),
        where('studentId', '==', studentId)
      )
    );

    const appointments: Appointment[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      appointments.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Appointment);
    });

    // Sort by date on the client side (most recent first)
    appointments.sort((a, b) => {
      const dateA = new Date(a.date || '1970-01-01');
      const dateB = new Date(b.date || '1970-01-01');
      return dateB.getTime() - dateA.getTime();
    });

    console.log(`📅 Retrieved ${appointments.length} appointments for student ${studentId}`);
    return appointments;
  } catch (error) {
    console.error('❌ Error fetching student appointments:', error);
    return [];
  }
};



/**
 * Get user-specific appointment statistics
 */
export const getUserAppointmentStats = async (userId: string, userRole: 'doctor' | 'student') => {
  try {
    const field = userRole === 'doctor' ? 'doctorId' : 'studentId';
    const appointmentsQuery = query(
      collection(db, 'appointments'),
      where(field, '==', userId)
    );

    const snapshot = await getDocs(appointmentsQuery);
    const appointments = snapshot.docs.map(doc => doc.data());

    const today = new Date().toISOString().split('T')[0];

    const total = appointments.length;
    const scheduled = appointments.filter(a => a.status === 'scheduled').length;
    const completed = appointments.filter(a => a.status === 'completed').length;
    const cancelled = appointments.filter(a => a.status === 'cancelled').length;
    const todayAppointments = appointments.filter(a => a.date === today).length;

    return {
      total,
      scheduled,
      completed,
      cancelled,
      todayAppointments
    };
  } catch (error) {
    console.error('Error getting user appointment stats:', error);
    return { total: 0, scheduled: 0, completed: 0, cancelled: 0, todayAppointments: 0 };
  }
};

/**
 * Create sample appointments for testing
 */
export const createSampleAppointments = async (): Promise<void> => {
  const sampleAppointments = [
    {
      studentId: 'student1',
      studentName: 'John Smith',
      doctorId: 'doctor1',
      doctorName: 'Dr. Sarah Johnson',
      date: new Date().toISOString().split('T')[0],
      time: '10:00 AM',
      status: 'scheduled' as const,
      type: 'consultation' as const,
      notes: 'Regular checkup'
    },
    {
      studentId: 'student2',
      studentName: 'Emma Davis',
      doctorId: 'doctor2',
      doctorName: 'Dr. Michael Chen',
      date: new Date().toISOString().split('T')[0],
      time: '11:30 AM',
      status: 'completed' as const,
      type: 'follow-up' as const,
      notes: 'Follow-up for anxiety treatment'
    },
    {
      studentId: 'student3',
      studentName: 'Alex Rodriguez',
      doctorId: 'doctor1',
      doctorName: 'Dr. Sarah Johnson',
      date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
      time: '09:15 AM',
      status: 'scheduled' as const,
      type: 'routine' as const,
      notes: 'Annual physical exam'
    }
  ];

  try {
    for (const appointment of sampleAppointments) {
      await createAppointment(appointment);
    }
    console.log('✅ Sample appointments created');
  } catch (error) {
    console.error('❌ Error creating sample appointments:', error);
  }
};
