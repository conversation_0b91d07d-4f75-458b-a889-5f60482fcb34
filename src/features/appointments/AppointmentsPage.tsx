import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>, Typo<PERSON>, Box, Paper, Grid, Button,
  TextField, MenuItem, Tabs, Tab, Card, CardContent,
  CircularProgress, Divider, IconButton, Chip, Dialog,
  DialogTitle, DialogContent, DialogActions, FormControl,
  InputLabel, Select, FormHelperText, Avatar, List, ListItem,
  ListItemAvatar, ListItemText, ListItemSecondaryAction
} from '@mui/material';
import {
  CalendarMonth as CalendarIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  LocationOn as LocationIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Videocam as VideocamIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import { useFirebase } from '../../contexts/FirebaseContext';
import Layout from '../../components/layout/Layout';

// Appointment type definition
interface Appointment {
  id: string;
  doctorName: string;
  doctorSpecialty: string;
  date: string;
  time: string;
  duration: number;
  location: string;
  type: 'in-person' | 'video' | 'phone';
  notes: string;
  status: 'scheduled' | 'completed' | 'cancelled';
}

// Doctor type definition
interface Doctor {
  id: string;
  name: string;
  specialty: string;
  image: string;
}

const AppointmentsPage = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState<Appointment | null>(null);
  const [newAppointment, setNewAppointment] = useState<Partial<Appointment>>({
    doctorName: '',
    doctorSpecialty: '',
    date: new Date().toISOString().split('T')[0],
    time: '09:00',
    duration: 30,
    location: '',
    type: 'in-person',
    notes: '',
    status: 'scheduled'
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [activeTab, setActiveTab] = useState(0);
  const { currentUser } = useFirebase();

  useEffect(() => {
    const loadAppointmentsAndDoctors = async () => {
      if (!currentUser?.uid) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Import services dynamically
        const { getAppointmentsByStudent } = await import('../../services/appointmentService');
        const { getDoctors } = await import('../../services/userManagementService');

        // Load appointments for current user
        const userAppointments = await getAppointmentsByStudent(currentUser.uid);

        // Transform appointments to match the component's interface
        const transformedAppointments: Appointment[] = userAppointments.map(apt => ({
          id: apt.id || '',
          doctorName: apt.doctorName,
          doctorSpecialty: 'General Practice', // Default specialty
          date: apt.date,
          time: apt.time,
          duration: 30, // Default duration
          location: apt.type === 'consultation' ? 'University Health Center' : '',
          type: apt.type === 'consultation' ? 'in-person' : 'video',
          notes: apt.notes || '',
          status: apt.status as 'scheduled' | 'completed' | 'cancelled'
        }));

        // Load doctors
        const doctorsList = await getDoctors();
        const transformedDoctors: Doctor[] = doctorsList.map(doc => ({
          id: doc.uid,
          name: doc.displayName || doc.email || 'Doctor',
          specialty: doc.specialty || 'General Practice',
          image: doc.displayName ? doc.displayName.split(' ').map(n => n[0]).join('') : 'DR'
        }));

        setAppointments(transformedAppointments);
        setDoctors(transformedDoctors);
      } catch (error) {
        console.error('Error loading appointments and doctors:', error);
        // Set empty arrays on error
        setAppointments([]);
        setDoctors([]);
      } finally {
        setLoading(false);
      }
    };

    loadAppointmentsAndDoctors();
  }, [currentUser?.uid]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleOpenAddDialog = () => {
    setEditingAppointment(null);
    setNewAppointment({
      doctorName: '',
      doctorSpecialty: '',
      date: new Date().toISOString().split('T')[0],
      time: '09:00',
      duration: 30,
      location: '',
      type: 'in-person',
      notes: '',
      status: 'scheduled'
    });
    setErrors({});
    setOpenAddDialog(true);
  };

  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
  };

  const handleEditAppointment = (appointment: Appointment) => {
    setEditingAppointment(appointment);
    setNewAppointment({ ...appointment });
    setErrors({});
    setOpenAddDialog(true);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setNewAppointment({ ...newAppointment, [name]: value });

    // Clear error when field is edited
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  const handleSelectChange = (event: any) => {
    const { name, value } = event.target;

    if (name === 'doctorName') {
      const selectedDoctor = doctors.find(doc => doc.name === value);
      if (selectedDoctor) {
        setNewAppointment({
          ...newAppointment,
          doctorName: value,
          doctorSpecialty: selectedDoctor.specialty
        });
      }
    } else {
      setNewAppointment({ ...newAppointment, [name]: value });
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!newAppointment.doctorName?.trim()) {
      newErrors.doctorName = 'Doctor name is required';
    }

    if (!newAppointment.date) {
      newErrors.date = 'Date is required';
    }

    if (!newAppointment.time) {
      newErrors.time = 'Time is required';
    }

    if (newAppointment.type === 'in-person' && !newAppointment.location?.trim()) {
      newErrors.location = 'Location is required for in-person appointments';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveAppointment = async () => {
    if (!validateForm() || !currentUser?.uid) return;

    setLoading(true);

    try {
      const { createAppointment, updateAppointment } = await import('../../services/appointmentService');

      if (editingAppointment) {
        // Update existing appointment
        await updateAppointment(editingAppointment.id, {
          studentId: currentUser.uid,
          studentName: currentUser.displayName || currentUser.email || 'Student',
          doctorId: '', // Would need to get doctor ID from selected doctor
          doctorName: newAppointment.doctorName || '',
          date: newAppointment.date || '',
          time: newAppointment.time || '',
          status: newAppointment.status as any,
          type: newAppointment.type === 'in-person' ? 'consultation' : 'follow-up',
          notes: newAppointment.notes
        });

        // Update local state
        const updatedAppointments = appointments.map(app =>
          app.id === editingAppointment.id ? { ...newAppointment, id: app.id } as Appointment : app
        );
        setAppointments(updatedAppointments);
      } else {
        // Create new appointment
        const appointmentData = {
          studentId: currentUser.uid,
          studentName: currentUser.displayName || currentUser.email || 'Student',
          doctorId: '', // Would need to get doctor ID from selected doctor
          doctorName: newAppointment.doctorName || '',
          date: newAppointment.date || '',
          time: newAppointment.time || '',
          status: 'scheduled' as const,
          type: newAppointment.type === 'in-person' ? 'consultation' : 'follow-up' as const,
          notes: newAppointment.notes
        };

        const appointmentId = await createAppointment(appointmentData);

        // Add to local state
        const newApp: Appointment = {
          ...newAppointment as Appointment,
          id: appointmentId
        };
        setAppointments([...appointments, newApp]);
      }

      setOpenAddDialog(false);
    } catch (error) {
      console.error('Error saving appointment:', error);
      alert('Failed to save appointment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAppointment = async (id: string) => {
    setLoading(true);

    try {
      const { deleteAppointment } = await import('../../services/appointmentService');
      await deleteAppointment(id);

      // Update local state
      const updatedAppointments = appointments.filter(app => app.id !== id);
      setAppointments(updatedAppointments);
    } catch (error) {
      console.error('Error deleting appointment:', error);
      alert('Failed to delete appointment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getAppointmentTypeIcon = (type: string) => {
    switch (type) {
      case 'in-person':
        return <LocationIcon />;
      case 'video':
        return <VideocamIcon />;
      case 'phone':
        return <PhoneIcon />;
      default:
        return <LocationIcon />;
    }
  };

  const getAppointmentTypeText = (type: string) => {
    switch (type) {
      case 'in-person':
        return 'In-person';
      case 'video':
        return 'Video Call';
      case 'phone':
        return 'Phone Call';
      default:
        return type;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'primary';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderAppointmentCards = (status: 'scheduled' | 'completed' | 'cancelled') => {
    const filteredApps = appointments.filter(app => app.status === status);

    if (filteredApps.length === 0) {
      return (
        <Box sx={{
          p: 4,
          textAlign: 'center',
          bgcolor: 'rgba(0,0,0,0.02)',
          borderRadius: 2
        }}>
          <CalendarIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No {status} appointments
          </Typography>
          {status === 'scheduled' && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenAddDialog}
              sx={{ mt: 2, borderRadius: 1 }}
            >
              Book New Appointment
            </Button>
          )}
        </Box>
      );
    }

    return (
      <Grid container spacing={3}>
        {filteredApps.map(appointment => (
          <Grid item xs={12} sm={6} md={4} key={appointment.id}>
            <Card
              elevation={0}
              sx={{
                borderRadius: 2,
                position: 'relative',
                border: '1px solid',
                borderColor: 'divider',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  borderColor: 'transparent'
                }
              }}
            >
              <Box
                sx={{
                  p: 1,
                  display: 'flex',
                  justifyContent: 'flex-end',
                  borderBottom: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Chip
                  label={status.charAt(0).toUpperCase() + status.slice(1)}
                  color={getStatusColor(status) as any}
                  size="small"
                  sx={{
                    borderRadius: 1,
                    fontWeight: 'medium',
                    fontSize: '0.75rem'
                  }}
                />
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 2 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" component="div" fontWeight="bold">
                    {new Date(appointment.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric', year: 'numeric' })}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {appointment.time} ({appointment.duration} min)
                    </Typography>
                  </Box>
                </Box>

                <Divider sx={{ my: 1.5 }} />

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: 'primary.light',
                      color: 'primary.main',
                      width: 40,
                      height: 40,
                      mr: 1.5
                    }}
                  >
                    {appointment.doctorName.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="body1" fontWeight="medium">
                      {appointment.doctorName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {appointment.doctorSpecialty}
                    </Typography>
                  </Box>
                </Box>

                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 1,
                    p: 1.5,
                    bgcolor: 'background.default',
                    borderRadius: 1
                  }}
                >
                  <Box sx={{ mr: 1.5 }}>
                    {appointment.type === 'in-person' && <LocationIcon color="primary" />}
                    {appointment.type === 'video' && <VideocamIcon color="success" />}
                    {appointment.type === 'phone' && <PhoneIcon color="info" />}
                  </Box>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {getAppointmentTypeText(appointment.type)}
                    </Typography>
                    {appointment.type === 'in-person' && appointment.location && (
                      <Typography variant="body2" color="text.secondary">
                        {appointment.location}
                      </Typography>
                    )}
                  </Box>
                </Box>

                {appointment.notes && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" color="text.secondary" fontWeight="medium">
                      Notes:
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 0.5 }}>
                      {appointment.notes}
                    </Typography>
                  </Box>
                )}
              </CardContent>

              {appointment.status === 'scheduled' && (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    p: 2,
                    borderTop: '1px solid',
                    borderColor: 'divider'
                  }}
                >
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<EditIcon />}
                    onClick={() => handleEditAppointment(appointment)}
                    sx={{ borderRadius: 1 }}
                  >
                    Edit
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleDeleteAppointment(appointment.id)}
                    sx={{ borderRadius: 1 }}
                  >
                    Cancel
                  </Button>
                </Box>
              )}
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
         <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Appointments
            </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleOpenAddDialog}
          >
            Book Appointment
          </Button>
        </Box>

        <Paper
          elevation={0}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'  // Updated to match dashboard style
          }}
        >
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2,
                fontWeight: 'medium'
              }
            }}
          >
            <Tab label="Upcoming" />
            <Tab label="Past" />
            <Tab label="Cancelled" />
          </Tabs>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 6 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ p: 3 }}>
              {activeTab === 0 && renderAppointmentCards('scheduled')}
              {activeTab === 1 && renderAppointmentCards('completed')}
              {activeTab === 2 && renderAppointmentCards('cancelled')}
            </Box>
          )}
        </Paper>

        {/* Add/Edit Appointment Dialog */}
        <Dialog
          open={openAddDialog}
          onClose={handleCloseAddDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {editingAppointment ? 'Edit Appointment' : 'Book New Appointment'}
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.doctorName}>
                  <InputLabel>Doctor</InputLabel>
                  <Select
                    name="doctorName"
                    value={newAppointment.doctorName || ''}
                    onChange={handleSelectChange}
                    label="Doctor"
                  >
                    {doctors.map(doctor => (
                      <MenuItem key={doctor.id} value={doctor.name}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar src={doctor.image} sx={{ width: 24, height: 24, mr: 1 }}>
                            {doctor.name.charAt(0)}
                          </Avatar>
                          <Typography>{doctor.name}</Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.doctorName && <FormHelperText>{errors.doctorName}</FormHelperText>}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  disabled
                  label="Specialty"
                  fullWidth
                  value={newAppointment.doctorSpecialty || ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="date"
                  label="Date"
                  type="date"
                  fullWidth
                  value={newAppointment.date || ''}
                  onChange={handleInputChange}
                  InputLabelProps={{ shrink: true }}
                  error={!!errors.date}
                  helperText={errors.date}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="time"
                  label="Time"
                  type="time"
                  fullWidth
                  value={newAppointment.time || ''}
                  onChange={handleInputChange}
                  InputLabelProps={{ shrink: true }}
                  error={!!errors.time}
                  helperText={errors.time}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Duration</InputLabel>
                  <Select
                    name="duration"
                    value={newAppointment.duration || 30}
                    onChange={handleSelectChange}
                    label="Duration"
                  >
                    <MenuItem value={15}>15 minutes</MenuItem>
                    <MenuItem value={30}>30 minutes</MenuItem>
                    <MenuItem value={45}>45 minutes</MenuItem>
                    <MenuItem value={60}>60 minutes</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Appointment Type</InputLabel>
                  <Select
                    name="type"
                    value={newAppointment.type || 'in-person'}
                    onChange={handleSelectChange}
                    label="Appointment Type"
                  >
                    <MenuItem value="in-person">In-person</MenuItem>
                    <MenuItem value="video">Video Call</MenuItem>
                    <MenuItem value="phone">Phone Call</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              {newAppointment.type === 'in-person' && (
                <Grid item xs={12}>
                  <TextField
                    name="location"
                    label="Location"
                    fullWidth
                    value={newAppointment.location || ''}
                    onChange={handleInputChange}
                    error={!!errors.location}
                    helperText={errors.location}
                    required
                  />
                </Grid>
              )}
              <Grid item xs={12}>
                <TextField
                  name="notes"
                  label="Notes"
                  multiline
                  rows={3}
                  fullWidth
                  value={newAppointment.notes || ''}
                  onChange={handleInputChange}
                  placeholder="Reason for visit or any special instructions"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseAddDialog}>Cancel</Button>
            <Button
              onClick={handleSaveAppointment}
              variant="contained"
              color="primary"
            >
              {editingAppointment ? 'Update' : 'Book'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default AppointmentsPage;


