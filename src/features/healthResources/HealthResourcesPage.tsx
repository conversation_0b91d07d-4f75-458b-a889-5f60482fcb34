import React, { useState, useEffect } from 'react';
import {
  Container, Typography, Box, Paper, Grid, Card, CardContent,
  CardMedia, Chip, TextField, InputAdornment, Tabs, Tab,
  Divider, Button, IconButton
} from '@mui/material';
import {
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import { getPublishedHealthResources } from '../../services/healthResourcesService';

type Resource = {
  id: string;
  title: string;
  category: string;
  author: string;
  publishDate: string;
  summary: string;
  imageUrl: string;
};

const HealthResourcesPage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [resources, setResources] = useState<Resource[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [savedResources, setSavedResources] = useState<string[]>([]);

  const categories = [
    'All', 'Mental Health', 'Nutrition', 'Wellness', 'Physical Health', 'Sexual Health', 'First Aid'
  ];

  useEffect(() => {
    setLoading(true);
    getPublishedHealthResources().then((data) => {
      setResources(data.map((resource: any) => ({
        id: resource.id || '',
        title: resource.title,
        category: resource.category || 'General',
        author: resource.authorName || 'Unknown',
        publishDate: resource.publishedAt ? new Date(resource.publishedAt.toDate()).toLocaleDateString() : '',
        summary: resource.summary || resource.content,
        imageUrl: resource.coverImageUrl || 'https://source.unsplash.com/random/800x600/?health'
      }))
      );
      setLoading(false);
    }).catch((error) => {
      console.error('Error loading health resources:', error);
      setLoading(false);
    });
  }, []);

  useEffect(() => {
    // Load saved resources from local storage
    const saved = localStorage.getItem('savedHealthResources');
    if (saved) {
      setSavedResources(JSON.parse(saved));
    }
  }, []);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const toggleSaveResource = (resourceId: string) => {
    let updatedSaved: string[];
    if (savedResources.includes(resourceId)) {
      updatedSaved = savedResources.filter(id => id !== resourceId);
    } else {
      updatedSaved = [...savedResources, resourceId];
    }
    setSavedResources(updatedSaved);
    localStorage.setItem('savedHealthResources', JSON.stringify(updatedSaved));
  };

  const filteredResources = resources.filter(resource => {
    // Filter by search term
    const matchesSearch =
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.author.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by category
    const selectedCategory = categories[activeTab];
    const matchesCategory = selectedCategory === 'All' || resource.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const savedFilteredResources = resources.filter(resource =>
    savedResources.includes(resource.id)
  );

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Health Resources
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Explore health articles and guides written by healthcare professionals
          </Typography>
        </Box>

        {/* Search Bar */}
        <Paper
          sx={{
            p: 2,
            mb: 4,
            display: 'flex',
            alignItems: 'center',
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <TextField
            fullWidth
            placeholder="Search for health resources..."
            value={searchTerm}
            onChange={handleSearch}
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
              }
            }}
          />
        </Paper>

        {/* Category Tabs */}
        <Paper
          sx={{
            mb: 4,
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2,
                fontWeight: 'medium'
              }
            }}
          >
            {categories.map((category, index) => (
              <Tab key={index} label={category} />
            ))}
            <Tab
              label="Saved"
              icon={<BookmarkIcon fontSize="small" />}
              iconPosition="start"
            />
          </Tabs>
        </Paper>

        {/* Resources Grid */}
        <Grid container spacing={3}>
          {loading ? (
            <Grid item xs={12}>
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography>Loading health resources...</Typography>
              </Box>
            </Grid>
          ) : activeTab === categories.length ? (
            // Saved resources tab
            savedFilteredResources.length === 0 ? (
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography>No saved resources yet</Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => setActiveTab(0)}
                    sx={{ mt: 2, borderRadius: 2 }}
                  >
                    Browse Resources
                  </Button>
                </Box>
              </Grid>
            ) : (
              savedFilteredResources.map(resource => (
                <Grid item xs={12} sm={6} md={4} key={resource.id}>
                  <ResourceCard
                    resource={resource}
                    isSaved={true}
                    onToggleSave={toggleSaveResource}
                    onClick={() => navigate(`/health-resources/${resource.id}`)}
                  />
                </Grid>
              ))
            )
          ) : filteredResources.length === 0 ? (
            <Grid item xs={12}>
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography>No resources found matching your search</Typography>
              </Box>
            </Grid>
          ) : (
            filteredResources.map(resource => (
              <Grid item xs={12} sm={6} md={4} key={resource.id}>
                <ResourceCard
                  resource={resource}
                  isSaved={savedResources.includes(resource.id)}
                  onToggleSave={toggleSaveResource}
                  onClick={() => navigate(`/health-resources/${resource.id}`)}
                />
              </Grid>
            ))
          )}
        </Grid>
      </Container>
    </Layout>
  );
};

// Resource Card Component
interface ResourceCardProps {
  resource: Resource;
  isSaved: boolean;
  onToggleSave: (id: string) => void;
  onClick: () => void;
}
const ResourceCard: React.FC<ResourceCardProps> = ({ resource, isSaved, onToggleSave, onClick }) => {
  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 3,
        overflow: 'hidden',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
          cursor: 'pointer'
        }
      }}
    >
      <Box sx={{ position: 'relative' }}>
        <CardMedia
          component="img"
          height="160"
          image={resource.imageUrl}
          alt={resource.title}
          onError={(e) => {
            console.log('Image failed to load:', resource.imageUrl);
            // Fallback to placeholder image
            (e.target as HTMLImageElement).src = 'https://source.unsplash.com/random/800x600/?health';
          }}
          onLoad={() => {
            console.log('Image loaded successfully:', resource.imageUrl);
          }}
        />
        <IconButton
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            bgcolor: 'rgba(255, 255, 255, 0.8)',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)',
            }
          }}
          onClick={(e) => {
            e.stopPropagation();
            onToggleSave(resource.id);
          }}
        >
          {isSaved ? (
            <BookmarkIcon color="primary" />
          ) : (
            <BookmarkBorderIcon />
          )}
        </IconButton>
      </Box>
      <CardContent
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column'
        }}
        onClick={onClick}
      >
        <Box sx={{ mb: 1 }}>
          <Chip
            label={resource.category}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>
        <Typography variant="h6" component="h2" gutterBottom>
          {resource.title}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flexGrow: 1 }}>
          {resource.summary}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
          <Typography variant="caption" color="text.secondary">
            By {resource.author}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {resource.publishDate}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default HealthResourcesPage;
