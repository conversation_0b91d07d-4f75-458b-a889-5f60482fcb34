import React, { useContext } from 'react';
import { useNotifications } from '../../hooks/useNotifications';
import { AuthContext } from '../../contexts/AuthContext';
import { Box, List, ListItem, ListItemText, Typography, Badge, IconButton } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';

const NotificationBell: React.FC = () => {
  const { currentUser } = useContext(AuthContext);
  const { notifications, loading } = useNotifications(currentUser?.uid || '');
  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Box sx={{ position: 'relative' }}>
      <IconButton color="inherit">
        <Badge badgeContent={unreadCount} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>
      {/* You can add a dropdown/popover here to show notification details */}
      {unreadCount > 0 && (
        <Box sx={{ position: 'absolute', right: 0, top: 40, bgcolor: 'white', boxShadow: 3, borderRadius: 2, minWidth: 300, zIndex: 10 }}>
          <List>
            {notifications.slice(0, 5).map(n => (
              <ListItem key={n.id}>
                <ListItemText
                  primary={<Typography fontWeight={n.read ? 'normal' : 'bold'}>{n.title}</Typography>}
                  secondary={n.message}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      )}
    </Box>
  );
};

export default NotificationBell;
