import { useEffect, useState } from 'react';
import { collection, onSnapshot, query, where, orderBy } from 'firebase/firestore';
import { db } from '../services/firebase';

export interface Notification {
  id?: string;
  userId: string;
  type: 'appointment' | 'medication' | 'health_tip' | 'record';
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
}

export const useNotifications = (userId: string) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const q = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const unsub = onSnapshot(q,
      (snap) => {
        try {
          const notificationsList = snap.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              ...data,
              createdAt: data.createdAt?.toDate?.() || new Date()
            } as Notification;
          });

          setNotifications(notificationsList);
          setLoading(false);
          console.log(`📢 Loaded ${notificationsList.length} notifications for user ${userId}`);
        } catch (err) {
          console.error('Error processing notifications:', err);
          setError('Failed to load notifications');
          setLoading(false);
        }
      },
      (err) => {
        console.error('Error listening to notifications:', err);
        setError('Failed to load notifications');
        setLoading(false);
      }
    );

    return () => unsub();
  }, [userId]);

  return { notifications, loading, error };
};

// Ensure this file has a .ts extension, not .js, and is imported using a relative path (not a URL) in your dashboard code.
// If you are using Vite or a similar dev server, make sure the import is:
// import { useNotifications } from '../../hooks/useNotifications';
// and not from a URL or with a .js extension.
