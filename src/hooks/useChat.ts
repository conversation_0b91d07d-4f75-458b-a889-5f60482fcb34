import { useState, useEffect, useCallback, useRef } from 'react';
import { chatService } from '../services/chatService';
import { useFirebase } from '../contexts/FirebaseContext';
import type { Conversation, Message, TypingIndicator } from '../types/chat';

export const useChat = () => {
  const { currentUser } = useFirebase();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [typing, setTyping] = useState<TypingIndicator[]>([]);

  // Refs for cleanup
  const conversationsUnsubscribe = useRef<(() => void) | null>(null);
  const messagesUnsubscribe = useRef<(() => void) | null>(null);
  const typingUnsubscribe = useRef<(() => void) | null>(null);
  const typingTimeout = useRef<NodeJS.Timeout | null>(null);

  // Load conversations
  useEffect(() => {
    if (!currentUser?.uid) return;

    setLoading(true);

    // Subscribe to conversations
    conversationsUnsubscribe.current = chatService.subscribeToConversations(
      currentUser.uid,
      (newConversations) => {
        setConversations(newConversations);
        setLoading(false);
      }
    );

    return () => {
      if (conversationsUnsubscribe.current) {
        conversationsUnsubscribe.current();
      }
    };
  }, [currentUser?.uid]);

  // Load messages when conversation is selected
  useEffect(() => {
    if (!selectedConversation) {
      setMessages([]);
      return;
    }

    setLoading(true);

    // Clean up previous subscription
    if (messagesUnsubscribe.current) {
      messagesUnsubscribe.current();
    }

    // Subscribe to messages
    messagesUnsubscribe.current = chatService.subscribeToMessages(
      selectedConversation.id,
      (newMessages) => {
        setMessages(newMessages);
        setLoading(false);

        // Mark messages as read
        if (currentUser?.uid) {
          chatService.markMessagesAsRead(selectedConversation.id, currentUser.uid);
        }
      }
    );

    // Subscribe to typing indicators
    if (typingUnsubscribe.current) {
      typingUnsubscribe.current();
    }

    typingUnsubscribe.current = chatService.subscribeToTyping(
      selectedConversation.id,
      setTyping
    );

    return () => {
      if (messagesUnsubscribe.current) {
        messagesUnsubscribe.current();
      }
      if (typingUnsubscribe.current) {
        typingUnsubscribe.current();
      }
    };
  }, [selectedConversation, currentUser?.uid]);

  // Create or get conversation
  const createConversation = useCallback(async (doctorId: string): Promise<string> => {
    if (!currentUser?.uid) {
      throw new Error('User not authenticated');
    }

    try {
      const conversationId = await chatService.createConversation(currentUser.uid, doctorId);

      // Find and select the conversation
      const conversation = conversations.find(c => c.id === conversationId);
      if (conversation) {
        setSelectedConversation(conversation);
      }

      return conversationId;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }, [currentUser?.uid, conversations]);

  // Send message
  const sendMessage = useCallback(async (content: string, type: string = 'text'): Promise<void> => {
    if (!selectedConversation || !currentUser?.uid || !content.trim()) {
      return;
    }

    try {
      setSending(true);
      await chatService.sendMessage(selectedConversation.id, currentUser.uid, content.trim(), type);
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    } finally {
      setSending(false);
    }
  }, [selectedConversation, currentUser?.uid]);

  // Set typing indicator
  const setTypingIndicator = useCallback(async (isTyping: boolean): Promise<void> => {
    if (!selectedConversation || !currentUser?.uid) {
      return;
    }

    try {
      if (isTyping) {
        await chatService.setTyping(
          selectedConversation.id,
          currentUser.uid,
          currentUser.displayName || currentUser.email || 'User'
        );
      }
    } catch (error) {
      console.error('Error setting typing indicator:', error);
    }
  }, [selectedConversation, currentUser]);

  // Handle typing with debounce
  const handleTyping = useCallback(() => {
    setTypingIndicator(true);

    // Clear existing timeout
    if (typingTimeout.current) {
      clearTimeout(typingTimeout.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeout.current = setTimeout(() => {
      setTypingIndicator(false);
    }, 1000);
  }, [setTypingIndicator]);

  // Select conversation
  const selectConversation = useCallback((conversation: Conversation) => {
    setSelectedConversation(conversation);
  }, []);

  // Get other participant in conversation
  const getOtherParticipant = useCallback((conversation: Conversation) => {
    if (!currentUser?.uid) return null;

    const otherParticipantId = conversation.participants.find(id => id !== currentUser.uid);
    return otherParticipantId ? conversation.participantDetails[otherParticipantId] : null;
  }, [currentUser?.uid]);

  // Get unread count for user
  const getUnreadCount = useCallback((conversation: Conversation) => {
    if (!currentUser?.uid) return 0;
    return conversation.unreadCounts[currentUser.uid] || 0;
  }, [currentUser?.uid]);

  // Get typing users (excluding current user)
  const getTypingUsers = useCallback(() => {
    if (!currentUser?.uid) return [];
    return typing.filter(t => t.userId !== currentUser.uid);
  }, [typing, currentUser?.uid]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (conversationsUnsubscribe.current) {
        conversationsUnsubscribe.current();
      }
      if (messagesUnsubscribe.current) {
        messagesUnsubscribe.current();
      }
      if (typingUnsubscribe.current) {
        typingUnsubscribe.current();
      }
      if (typingTimeout.current) {
        clearTimeout(typingTimeout.current);
      }
    };
  }, []);

  return {
    // State
    conversations,
    selectedConversation,
    messages,
    loading,
    sending,
    typing: getTypingUsers(),

    // Actions
    createConversation,
    selectConversation,
    sendMessage,
    handleTyping,

    // Helpers
    getOtherParticipant,
    getUnreadCount
  };
};

/**
 * Standalone function to create conversation between two users
 * Can be used outside of the useChat hook
 */
export const createConversationBetweenUsers = async (userId: string, doctorId: string): Promise<string> => {
  const { chatService } = await import('../services/chatService');
  return await chatService.createConversation(userId, doctorId);
};
