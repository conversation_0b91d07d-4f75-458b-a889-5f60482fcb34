import React, { useState, useEffect } from 'react';
import {
  Container, Typography, Box, Paper, Button,
  List, ListItem, ListItemText, ListItemIcon, Divider,
  IconButton, TextField, Dialog, DialogTitle, DialogContent,
  DialogActions, CircularProgress, Chip, Alert, LinearProgress
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  InsertDriveFile as GenericFileIcon,
  Folder as FolderIcon,
  CreateNewFolder as CreateFolderIcon
} from '@mui/icons-material';
import { useFirebase } from '../contexts/FirebaseContext';
import Layout from '../components/layout/Layout';
import { useSupabaseFileUpload } from '../hooks/useSupabaseFileUpload';
import { supabase } from '../services/supabase';

type FileCategory = 'medical_records' | 'lab_results' | 'imaging' | 'prescriptions';

interface FileItem {
  id: string;
  name: string;
  type: string;
  size: string;
  date: string;
  url?: string;
  path?: string;
}

interface FolderItem {
  id: string;
  name: string;
  date: string;
}

const StudentMedicalRecordsUpload: React.FC = () => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [currentFolder, setCurrentFolder] = useState<FolderItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [newFolderDialogOpen, setNewFolderDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileCategory, setFileCategory] = useState<FileCategory>('medical_records');
  const [currentPath, setCurrentPath] = useState<string[]>([]);
  const { currentUser } = useFirebase();
  const { uploadState, uploadMedia, resetUploadState } = useSupabaseFileUpload();

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <PdfIcon color="error" />;
    if (fileType.includes('image')) return <ImageIcon color="primary" />;
    return <GenericFileIcon />;
  };

  // Load files and folders from Supabase
  useEffect(() => {
    const loadFilesAndFolders = async () => {
      if (!currentUser) return;

      setLoading(true);
      try {
        // Build current path
        const basePath = `${currentUser.uid}/MEDICAL_RECORDS`;
        const fullPath = currentPath.length > 0 ? `${basePath}/${currentPath.join('/')}` : basePath;

        const { data: supabaseItems, error } = await supabase.storage
          .from('medical-records')
          .list(fullPath);

        if (!error && supabaseItems) {
          // Separate files and folders
          const fileData: FileItem[] = [];
          const folderData: FolderItem[] = [];

          supabaseItems.forEach(item => {
            if (item.metadata) {
              // It's a file
              fileData.push({
                id: item.id || item.name,
                name: item.name,
                type: item.metadata.mimetype || 'unknown',
                size: formatFileSize(item.metadata.size || 0),
                date: item.metadata.lastModified ? new Date(item.metadata.lastModified).toLocaleDateString() : new Date().toLocaleDateString(),
                path: `${fullPath}/${item.name}`
              });
            } else {
              // It's a folder
              folderData.push({
                id: item.name,
                name: item.name,
                date: new Date().toLocaleDateString() // Folders don't have metadata
              });
            }
          });

          setFiles(fileData);
          setFolders(folderData);
        }
      } catch (error) {
        console.error('Error loading files and folders:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFilesAndFolders();
  }, [currentUser, currentPath]);

  const handleUploadFile = async () => {
    if (!selectedFile || !currentUser) return;

    try {
      const result = await uploadMedia(currentUser.uid, selectedFile, 'MEDICAL_RECORDS');

      if (result?.fileUrl) {
        console.log('File uploaded successfully:', result);
        handleCloseUploadDialog();
        // Refresh the file list
        const path = `${currentUser.uid}/MEDICAL_RECORDS`;
        const { data: supabaseFiles, error } = await supabase.storage
          .from('medical-records')
          .list(path);

        if (!error && supabaseFiles) {
          const fileData: FileItem[] = supabaseFiles
            .filter(item => item.metadata)
            .map(file => ({
              id: file.id || file.name,
              name: file.name,
              type: file.metadata?.mimetype || 'unknown',
              size: formatFileSize(file.metadata?.size || 0),
              date: file.metadata?.lastModified ? new Date(file.metadata.lastModified).toLocaleDateString() : new Date().toLocaleDateString(),
              path: `${path}/${file.name}`
            }));

          setFiles(fileData);
        }
      }
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  };

  const handleOpenUploadDialog = () => {
    setUploadDialogOpen(true);
  };

  const handleCloseUploadDialog = () => {
    setUploadDialogOpen(false);
    setSelectedFile(null);
    resetUploadState();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleDownloadFile = async (fileName: string, filePath?: string) => {
    try {
      const fullPath = filePath || `${currentUser?.uid}/MEDICAL_RECORDS/${fileName}`;

      const { data, error } = await supabase.storage
        .from('medical-records')
        .download(fullPath);

      if (error) {
        console.error('Download error:', error);
        const { data: urlData } = supabase.storage
          .from('medical-records')
          .getPublicUrl(fullPath);
        window.open(urlData.publicUrl, '_blank');
        return;
      }

      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      alert('Failed to download file. Please try again.');
    }
  };

  const handleDeleteFile = async (fileName: string, filePath?: string) => {
    try {
      const fullPath = filePath || `${currentUser?.uid}/MEDICAL_RECORDS/${fileName}`;

      const { error } = await supabase.storage
        .from('medical-records')
        .remove([fullPath]);

      if (error) {
        console.error('Delete error:', error);
        alert('Failed to delete file. Please try again.');
        return;
      }

      // Refresh the file list by re-running the effect
      const basePath = `${currentUser?.uid}/MEDICAL_RECORDS`;
      const fullCurrentPath = currentPath.length > 0 ? `${basePath}/${currentPath.join('/')}` : basePath;

      const { data: supabaseItems, error: listError } = await supabase.storage
        .from('medical-records')
        .list(fullCurrentPath);

      if (!listError && supabaseItems) {
        const fileData: FileItem[] = [];
        const folderData: FolderItem[] = [];

        supabaseItems.forEach(item => {
          if (item.metadata) {
            fileData.push({
              id: item.id || item.name,
              name: item.name,
              type: item.metadata.mimetype || 'unknown',
              size: formatFileSize(item.metadata.size || 0),
              date: item.metadata.lastModified ? new Date(item.metadata.lastModified).toLocaleDateString() : new Date().toLocaleDateString(),
              path: `${fullCurrentPath}/${item.name}`
            });
          } else {
            folderData.push({
              id: item.name,
              name: item.name,
              date: new Date().toLocaleDateString()
            });
          }
        });

        setFiles(fileData);
        setFolders(folderData);
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      alert('Failed to delete file. Please try again.');
    }
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim() || !currentUser) return;

    try {
      // Create a placeholder file in the new folder to ensure it exists
      const basePath = `${currentUser.uid}/MEDICAL_RECORDS`;
      const fullPath = currentPath.length > 0 ? `${basePath}/${currentPath.join('/')}` : basePath;
      const folderPath = `${fullPath}/${newFolderName.trim()}/.placeholder`;

      const placeholderFile = new File([''], '.placeholder', { type: 'text/plain' });

      const { error } = await supabase.storage
        .from('medical-records')
        .upload(folderPath, placeholderFile);

      if (error) {
        console.error('Error creating folder:', error);
        alert('Failed to create folder. Please try again.');
        return;
      }

      // Refresh the file list
      const { data: supabaseItems, error: listError } = await supabase.storage
        .from('medical-records')
        .list(fullPath);

      if (!listError && supabaseItems) {
        const fileData: FileItem[] = [];
        const folderData: FolderItem[] = [];

        supabaseItems.forEach(item => {
          if (item.metadata) {
            fileData.push({
              id: item.id || item.name,
              name: item.name,
              type: item.metadata.mimetype || 'unknown',
              size: formatFileSize(item.metadata.size || 0),
              date: item.metadata.lastModified ? new Date(item.metadata.lastModified).toLocaleDateString() : new Date().toLocaleDateString(),
              path: `${fullPath}/${item.name}`
            });
          } else {
            folderData.push({
              id: item.name,
              name: item.name,
              date: new Date().toLocaleDateString()
            });
          }
        });

        setFiles(fileData);
        setFolders(folderData);
      }

      setNewFolderDialogOpen(false);
      setNewFolderName('');
      console.log('Folder created successfully:', newFolderName);
    } catch (error) {
      console.error('Error creating folder:', error);
      alert('Failed to create folder. Please try again.');
    }
  };

  const handleOpenFolder = (folderName: string) => {
    setCurrentPath([...currentPath, folderName]);
  };

  const handleNavigateUp = () => {
    if (currentPath.length > 0) {
      setCurrentPath(currentPath.slice(0, -1));
    }
  };

  const handleBreadcrumbClick = (index: number) => {
    setCurrentPath(currentPath.slice(0, index + 1));
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            {currentPath.length > 0 ? currentPath[currentPath.length - 1] : 'Medical Records'}
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setNewFolderDialogOpen(true)}
              sx={{ mr: 1 }}
            >
              New Folder
            </Button>
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={handleOpenUploadDialog}
            >
              Upload File
            </Button>
          </Box>
        </Box>

        {currentPath.length > 0 && (
          <Button
            variant="text"
            onClick={handleNavigateUp}
            sx={{ mb: 2 }}
          >
            ← Back to {currentPath.length === 1 ? 'Medical Records' : currentPath[currentPath.length - 2]}
          </Button>
        )}

        <Paper
          sx={{
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ p: 3 }}>
              {/* Show folders first */}
              {folders.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Folders
                  </Typography>
                  <List>
                    {folders.map((folder) => (
                      <ListItem
                        key={folder.id}
                        secondaryAction={
                          <Typography variant="body2" color="text.secondary">
                            {folder.date}
                          </Typography>
                        }
                        sx={{
                          cursor: 'pointer',
                          '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                        }}
                        onClick={() => handleOpenFolder(folder.name)}
                      >
                        <ListItemIcon>
                          <FolderIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={folder.name}
                        />
                      </ListItem>
                    ))}
                  </List>
                  <Divider sx={{ my: 2 }} />
                </>
              )}

              {/* Show files */}
              <Typography variant="h6" gutterBottom>
                Files
              </Typography>
              {files.length === 0 ? (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {folders.length === 0
                      ? "No files found. Upload your first medical record to get started."
                      : "No files in this folder."
                    }
                  </Typography>
                </Box>
              ) : (
                <List>
                  {files.map((file) => (
                    <ListItem
                      key={file.id}
                      secondaryAction={
                        <Box>
                          <IconButton
                            aria-label="download"
                            onClick={() => handleDownloadFile(file.name, file.path)}
                          >
                            <DownloadIcon />
                          </IconButton>
                          <IconButton
                            aria-label="delete"
                            color="error"
                            onClick={() => handleDeleteFile(file.name, file.path)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      }
                    >
                      <ListItemIcon>
                        {getFileIcon(file.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={file.name}
                        secondary={
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <Chip
                              label={file.size}
                              size="small"
                              variant="outlined"
                            />
                            <Typography variant="body2" color="text.secondary">
                              {file.date}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          )}
        </Paper>
      </Container>


      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={handleCloseUploadDialog}>
        <DialogTitle>Upload Medical Record</DialogTitle>
        <DialogContent>
          <Box sx={{ minWidth: 400, mt: 2 }}>
            {!selectedFile ? (
              <Box
                sx={{
                  border: '2px dashed',
                  borderColor: 'divider',
                  borderRadius: 2,
                  p: 3,
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' }
                }}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Choose a file to upload
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Click here or drag and drop your medical record
                </Typography>
                <input
                  id="file-input"
                  type="file"
                  hidden
                  onChange={handleFileSelect}
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                />
              </Box>
            ) : (
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Selected File
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedFile.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Size: {formatFileSize(selectedFile.size)}
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => setSelectedFile(null)}
                  sx={{ mr: 1 }}
                >
                  Choose Different File
                </Button>
              </Box>
            )}

            {uploadState.isUploading && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress variant="determinate" value={uploadState.progress} />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Uploading... {uploadState.progress}%
                </Typography>
              </Box>
            )}

            {uploadState.error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {uploadState.error}
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUploadDialog}>Cancel</Button>
          <Button
            onClick={handleUploadFile}
            variant="contained"
            disabled={!selectedFile || uploadState.isUploading}
          >
            {uploadState.isUploading ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* New Folder Dialog */}
      <Dialog open={newFolderDialogOpen} onClose={() => setNewFolderDialogOpen(false)}>
        <DialogTitle>Create New Folder</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Folder Name"
            fullWidth
            variant="outlined"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleCreateFolder();
              }
            }}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewFolderDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateFolder}
            variant="contained"
            disabled={!newFolderName.trim()}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );


};

export default StudentMedicalRecordsUpload;
