import React, { useState } from 'react';
import { 
  Container, Typography, Box, Paper, Button, Grid, 
  TextField, FormControl, InputLabel, Select, MenuItem,
  Divider, Chip, IconButton, Snackbar, Alert
} from '@mui/material';
import { 
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  CloudUpload as CloudUploadIcon,
  Add as AddIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ReactQuill from 'react-quill'; // You'll need to install this package
import 'react-quill/dist/quill.snow.css';
import { createHealthResource } from '../../services/healthResourcesService';
import { useFirebase } from '../../contexts/FirebaseContext';

interface CoverImage {
  file: File;
  preview: string;
}

interface FormData {
  title: string;
  category: string;
  summary: string;
  content: string;
  tags: string[];
  status: 'draft' | 'published';
}

const HealthResourceCreate = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  const [loading, setLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState('');
  const [formData, setFormData] = useState<FormData>({
    title: '',
    category: '',
    summary: '',
    content: '',
    tags: [],
    status: 'draft'
  });
  const [newTag, setNewTag] = useState('');
  const [coverImage, setCoverImage] = useState<CoverImage | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement> | SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name as string]: value
    }));
  };

  const handleContentChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setCoverImage({
        file,
        preview: URL.createObjectURL(file)
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setSaveError('');
    try {
      if (!currentUser) throw new Error('You must be logged in as a doctor.');
      await createHealthResource({
        title: formData.title,
        content: formData.content,
        summary: formData.summary,
        category: formData.category,
        tags: formData.tags,
        authorId: currentUser.uid,
        authorName: currentUser.displayName || 'Doctor',
        coverImage: coverImage?.file,
        status: formData.status
      });
      setSaveSuccess(true);
      setTimeout(() => {
        navigate('/doctor/health-resources');
      }, 1500);
    } catch (err) {
      setSaveError((err as Error).message || 'Failed to create health resource.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Create Health Resource
          </Typography>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/doctor/health-resources')}
            sx={{ borderRadius: 2 }}
          >
            Cancel
          </Button>
        </Box>
        
        {/* Create Form */}
        <Paper 
          sx={{ 
            p: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  label="Title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Category</InputLabel>
                  <Select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    label="Category"
                  >
                    <MenuItem value="Mental Health">Mental Health</MenuItem>
                    <MenuItem value="Nutrition">Nutrition</MenuItem>
                    <MenuItem value="Wellness">Wellness</MenuItem>
                    <MenuItem value="Physical Health">Physical Health</MenuItem>
                    <MenuItem value="Sexual Health">Sexual Health</MenuItem>
                    <MenuItem value="First Aid">First Aid</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    label="Status"
                  >
                    <MenuItem value="draft">Save as Draft</MenuItem>
                    <MenuItem value="published">Publish Immediately</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  label="Summary"
                  name="summary"
                  value={formData.summary}
                  onChange={handleChange}
                  multiline
                  rows={2}
                  variant="outlined"
                  helperText="A brief description of the resource (max 200 characters)"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Cover Image
                </Typography>
                <Box 
                  sx={{ 
                    border: '1px dashed',
                    borderColor: 'divider',
                    borderRadius: 2,
                    p: 3,
                    textAlign: 'center',
                    mb: 2
                  }}
                >
                  {coverImage ? (
                    <Box>
                      <img 
                        src={coverImage.preview} 
                        alt="Cover preview" 
                        style={{ 
                          maxWidth: '100%', 
                          maxHeight: '200px',
                          objectFit: 'contain',
                          marginBottom: '16px'
                        }}
                      />
                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={() => setCoverImage(null)}
                      >
                        Remove Image
                      </Button>
                    </Box>
                  ) : (
                    <Button
                      component="label"
                      variant="outlined"
                      startIcon={<CloudUploadIcon />}
                    >
                      Upload Cover Image
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </Button>
                  )}
                </Box>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Content
                </Typography>
                <Box 
                  sx={{ 
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2,
                    mb: 2,
                    '.ql-container': {
                      borderBottomLeftRadius: 8,
                      borderBottomRightRadius: 8,
                    },
                    '.ql-toolbar': {
                      borderTopLeftRadius: 8,
                      borderTopRightRadius: 8,
                    }
                  }}
                >
                  <ReactQuill
                    value={formData.content}
                    onChange={handleContentChange}
                    style={{ height: '300px' }}
                    modules={{
                      toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['link', 'image'],
                        ['clean']
                      ]
                    }}
                  />
                </Box>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Tags
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TextField
                    label="Add a tag"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                    sx={{ mr: 1, flexGrow: 1 }}
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleAddTag}
                    sx={{ borderRadius: 2 }}
                  >
                    Add
                  </Button>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {formData.tags.map((tag, index) => (
                    <Chip
                      key={index}
                      label={tag}
                      onDelete={() => handleRemoveTag(tag)}
                      sx={{ mb: 1 }}
                    />
                  ))}
                </Box>
              </Grid>
              
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<SaveIcon />}
                  disabled={loading}
                  sx={{ borderRadius: 2 }}
                >
                  {formData.status === 'published' ? 'Publish Resource' : 'Save Draft'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </Paper>
        
        {/* Snackbar for notifications */}
        <Snackbar open={saveSuccess} autoHideDuration={6000} onClose={() => setSaveSuccess(false)}>
          <Alert onClose={() => setSaveSuccess(false)} severity="success">
            Health resource saved successfully!
          </Alert>
        </Snackbar>
        <Snackbar open={!!saveError} autoHideDuration={6000} onClose={() => setSaveError('')}>
          <Alert onClose={() => setSaveError('')} severity="error">
            {saveError}
          </Alert>
        </Snackbar>
      </Container>
    </Layout>
  );
};

export default HealthResourceCreate;


