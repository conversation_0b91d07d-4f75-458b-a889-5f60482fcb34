import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  Chip,
  Grid,
  Card,
  CardContent,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Person as PersonIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Email as EmailIcon,
  AccessTime as TimeIcon,
  Message as MessageIcon
} from '@mui/icons-material';
import Layout from '../../components/layout/Layout';
import { useFirebase } from '../../contexts/FirebaseContext';
import {
  getDoctorRequests,
  acceptConnectionRequest,
  rejectConnectionRequest,
  getDoctorRequestStats,
  debugRequestSystem,
  createTestRequest,
  type ConnectionRequest
} from '../../services/requestService';

const DoctorRequests = () => {
  const { currentUser } = useFirebase();
  const [requests, setRequests] = useState<ConnectionRequest[]>([]);
  const [stats, setStats] = useState({ total: 0, pending: 0, accepted: 0, rejected: 0 });
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<ConnectionRequest | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  // Load requests and stats
  useEffect(() => {
    const loadData = async () => {
      if (!currentUser?.uid) return;

      try {
        setLoading(true);
        const [requestsData, statsData] = await Promise.all([
          getDoctorRequests(currentUser.uid),
          getDoctorRequestStats(currentUser.uid)
        ]);

        setRequests(requestsData);
        setStats(statsData);
      } catch (error) {
        console.error('Error loading requests:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentUser?.uid]);

  const handleAcceptRequest = async (requestId: string) => {
    try {
      setActionLoading(true);
      console.log(`🔄 Accepting request: ${requestId}`);

      const success = await acceptConnectionRequest(requestId);

      if (success) {
        console.log(`✅ Request accepted successfully`);

        // Remove from pending requests
        setRequests(prev => prev.filter(req => req.id !== requestId));
        setStats(prev => ({
          ...prev,
          pending: prev.pending - 1,
          accepted: prev.accepted + 1
        }));
        setDialogOpen(false);
        setSelectedRequest(null);

        // Show success message
        alert('✅ Connection request accepted!\n\nThe student has been notified and can now chat with you. A conversation has been created between you and the student.');
      } else {
        alert('❌ Failed to accept request. Please try again.');
      }
    } catch (error) {
      console.error('Error accepting request:', error);
      alert('❌ An error occurred while accepting the request. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectRequest = async (requestId: string) => {
    try {
      setActionLoading(true);
      const success = await rejectConnectionRequest(requestId);

      if (success) {
        // Remove from pending requests
        setRequests(prev => prev.filter(req => req.id !== requestId));
        setStats(prev => ({
          ...prev,
          pending: prev.pending - 1,
          rejected: prev.rejected + 1
        }));
        setDialogOpen(false);
        setSelectedRequest(null);
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleViewRequest = (request: ConnectionRequest) => {
    setSelectedRequest(request);
    setDialogOpen(true);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                Connection Requests
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage student requests to connect with you for healthcare consultation
              </Typography>
            </Box>

            {/* Debug Tools */}
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                size="small"
                variant="outlined"
                onClick={async () => {
                  if (!currentUser?.uid) return;
                  const result = await debugRequestSystem(currentUser.uid);
                  console.log('Debug result:', result);
                  alert(`Debug Results:\n- Total requests in DB: ${result.totalRequests || 0}\n- Your requests: ${result.doctorRequests || 0}\n- Available students: ${result.availableStudents || 0}\n\nCheck console for details.`);
                }}
              >
                Debug System
              </Button>
              <Button
                size="small"
                variant="contained"
                color="secondary"
                onClick={async () => {
                  if (!currentUser?.uid) return;
                  const result = await createTestRequest(currentUser.uid);
                  if (result.success) {
                    alert(`✅ Test request created from ${result.studentName}!\n\nRefreshing page...`);
                    window.location.reload();
                  } else {
                    alert(`❌ Failed to create test request: ${result.error}`);
                  }
                }}
              >
                Create Test Request
              </Button>
            </Box>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="warning.main">
                      {stats.pending}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Requests
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'warning.light', color: 'warning.main' }}>
                    <TimeIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="success.main">
                      {stats.accepted}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Accepted
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.light', color: 'success.main' }}>
                    <CheckIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="error.main">
                      {stats.rejected}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Rejected
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'error.light', color: 'error.main' }}>
                    <CloseIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="primary.main">
                      {stats.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Requests
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'primary.light', color: 'primary.main' }}>
                    <PersonIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Requests List */}
        <Paper sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Pending Requests ({requests.length})
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {requests.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 6 }}>
                <PersonIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No pending requests
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  When students request to connect with you, they'll appear here
                </Typography>
              </Box>
            ) : (
              <List>
                {requests.map((request, index) => (
                  <React.Fragment key={request.id}>
                    <ListItem
                      sx={{
                        borderRadius: 2,
                        mb: 1,
                        '&:hover': { bgcolor: 'rgba(0,114,255,0.04)' }
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.light', color: 'primary.main' }}>
                          {request.studentName.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {request.studentName}
                            </Typography>
                            <Chip
                              label="Pending"
                              size="small"
                              color="warning"
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <EmailIcon sx={{ fontSize: 16 }} />
                              {request.studentEmail}
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                              <TimeIcon sx={{ fontSize: 14 }} />
                              Requested on {formatDate(request.createdAt)}
                            </Typography>
                            {request.message && (
                              <Typography variant="body2" sx={{ mt: 0.5, fontStyle: 'italic' }}>
                                <MessageIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                                "{request.message}"
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => handleViewRequest(request)}
                        >
                          View Details
                        </Button>
                        <Button
                          variant="contained"
                          color="success"
                          size="small"
                          startIcon={<CheckIcon />}
                          onClick={() => handleAcceptRequest(request.id)}
                          disabled={actionLoading}
                        >
                          Accept
                        </Button>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          startIcon={<CloseIcon />}
                          onClick={() => handleRejectRequest(request.id)}
                          disabled={actionLoading}
                        >
                          Reject
                        </Button>
                      </Box>
                    </ListItem>
                    {index < requests.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Box>
        </Paper>

        {/* Request Details Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            Connection Request Details
          </DialogTitle>
          <DialogContent>
            {selectedRequest && (
              <Box sx={{ py: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {selectedRequest.studentName}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedRequest.studentEmail}
                </Typography>
                <Typography variant="body2" sx={{ mt: 2, mb: 2 }}>
                  <strong>Requested on:</strong> {formatDate(selectedRequest.createdAt)}
                </Typography>
                {selectedRequest.message && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" fontWeight="medium" gutterBottom>
                      Message from student:
                    </Typography>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <Typography variant="body2">
                        {selectedRequest.message}
                      </Typography>
                    </Paper>
                  </Box>
                )}
                <Alert severity="info" sx={{ mt: 2 }}>
                  By accepting this request, the student will be able to chat with you and book appointments.
                </Alert>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>
              Close
            </Button>
            {selectedRequest && (
              <>
                <Button
                  color="error"
                  onClick={() => handleRejectRequest(selectedRequest.id)}
                  disabled={actionLoading}
                >
                  Reject
                </Button>
                <Button
                  variant="contained"
                  color="success"
                  onClick={() => handleAcceptRequest(selectedRequest.id)}
                  disabled={actionLoading}
                >
                  {actionLoading ? <CircularProgress size={20} /> : 'Accept Request'}
                </Button>
              </>
            )}
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default DoctorRequests;
