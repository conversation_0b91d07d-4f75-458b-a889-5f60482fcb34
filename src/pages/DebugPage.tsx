import React, { useState } from 'react';
import {
  Container, Typo<PERSON>, Box, Paper, Button, Grid,
  Alert, CircularProgress, Accordion, AccordionSummary,
  AccordionDetails, List, ListItem, ListItemText
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useAuth } from '../contexts/AuthContext';
import Layout from '../components/layout/Layout';

const DebugPage = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>({});

  const runTest = async (testName: string, testFunction: () => Promise<any>) => {
    setLoading(true);
    try {
      console.log(`🧪 Running test: ${testName}`);
      const result = await testFunction();
      setResults(prev => ({ ...prev, [testName]: result }));
      console.log(`✅ Test ${testName} completed:`, result);
    } catch (error) {
      console.error(`❌ Test ${testName} failed:`, error);
      setResults(prev => ({ 
        ...prev, 
        [testName]: { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        } 
      }));
    } finally {
      setLoading(false);
    }
  };

  const testHealthResources = () => runTest('healthResources', async () => {
    const testFn = (window as any).testHealthResources;
    if (!testFn) throw new Error('testHealthResources function not available');
    return await testFn();
  });

  const debugHealthCollection = () => runTest('healthCollection', async () => {
    const debugFn = (window as any).debugHealthResourcesCollection;
    if (!debugFn) throw new Error('debugHealthResourcesCollection function not available');
    return await debugFn();
  });

  const testSupabaseBucket = () => runTest('supabaseBucket', async () => {
    const testFn = (window as any).testSupabaseBucket;
    if (!testFn) throw new Error('testSupabaseBucket function not available');
    return await testFn();
  });

  const debugMedicalRecords = () => runTest('medicalRecords', async () => {
    if (!currentUser?.uid) throw new Error('User not logged in');
    const debugFn = (window as any).debugMedicalRecords;
    if (!debugFn) throw new Error('debugMedicalRecords function not available');
    return await debugFn(currentUser.uid);
  });

  const runAllTests = async () => {
    await testHealthResources();
    await debugHealthCollection();
    await testSupabaseBucket();
    if (currentUser?.uid) {
      await debugMedicalRecords();
    }
  };

  const renderResult = (testName: string, result: any) => {
    if (!result) return null;

    return (
      <Accordion key={testName}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">
            {testName} {result.success ? '✅' : '❌'}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box>
            {result.error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {result.error}
              </Alert>
            )}
            
            {result.success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Test passed successfully!
              </Alert>
            )}

            {/* Health Resources Results */}
            {testName === 'healthResources' && result.resources && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Found {result.count} health resources:
                </Typography>
                <List>
                  {result.resources.slice(0, 5).map((resource: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={resource.title}
                        secondary={`${resource.category} • ${resource.authorName}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {/* Health Collection Results */}
            {testName === 'healthCollection' && result.documents && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Collection Stats: {result.total} total, {result.published} published
                </Typography>
                <List>
                  {result.documents.slice(0, 5).map((doc: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={doc.title}
                        secondary={`Status: ${doc.status} • Author: ${doc.authorName}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {/* Medical Records Results */}
            {testName === 'medicalRecords' && result.files && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Found {result.count} medical record files:
                </Typography>
                <List>
                  {result.files.map((file: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={file.name}
                        secondary={`Size: ${file.metadata?.size || 'Unknown'} bytes`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            <Typography variant="body2" component="pre" sx={{ 
              mt: 2, 
              p: 2, 
              bgcolor: 'grey.100', 
              borderRadius: 1,
              fontSize: '0.8rem',
              overflow: 'auto',
              maxHeight: 200
            }}>
              {JSON.stringify(result, null, 2)}
            </Typography>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Debug Dashboard
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          Use this page to debug issues with health resources and medical records.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Individual Tests
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button 
                  variant="outlined" 
                  onClick={testHealthResources}
                  disabled={loading}
                >
                  Test Health Resources
                </Button>
                
                <Button 
                  variant="outlined" 
                  onClick={debugHealthCollection}
                  disabled={loading}
                >
                  Debug Health Collection
                </Button>
                
                <Button 
                  variant="outlined" 
                  onClick={testSupabaseBucket}
                  disabled={loading}
                >
                  Test Supabase Bucket
                </Button>
                
                <Button 
                  variant="outlined" 
                  onClick={debugMedicalRecords}
                  disabled={loading || !currentUser?.uid}
                >
                  Debug Medical Records
                </Button>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button 
                  variant="contained" 
                  onClick={runAllTests}
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Run All Tests'}
                </Button>
                
                <Button 
                  variant="outlined" 
                  onClick={() => setResults({})}
                >
                  Clear Results
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>

        {Object.keys(results).length > 0 && (
          <Box sx={{ mt: 4 }}>
            <Typography variant="h5" gutterBottom>
              Test Results
            </Typography>
            
            {Object.entries(results).map(([testName, result]) => 
              renderResult(testName, result)
            )}
          </Box>
        )}
      </Container>
    </Layout>
  );
};

export default DebugPage;
