{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.0.1", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@mui/x-date-pickers": "^8.5.2", "@supabase/supabase-js": "^2.50.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "firebase": "^10.7.0", "framer-motion": "^10.18.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-quill": "^2.0.0", "react-router-dom": "^6.20.0", "recharts": "^2.15.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.8", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}